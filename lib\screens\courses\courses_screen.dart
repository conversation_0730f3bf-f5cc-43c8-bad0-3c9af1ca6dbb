import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';
import '../../widgets/course_card.dart';

class CoursesScreen extends StatefulWidget {
  const CoursesScreen({super.key});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen> {
  String _searchQuery = '';
  EducationLevel? _selectedLevel;
  CourseCategory? _selectedCategory;
  String? _selectedSubjectId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Cours'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filtrer',
          ),
          IconButton(
            icon: const Icon(Icons.subject),
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.subjects);
            },
            tooltip: 'G<PERSON>rer les matières',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              _buildSearchBar(),
              _buildFilterSummary(appProvider),
              _buildQuickStats(appProvider),
              Expanded(child: _buildCoursesList(appProvider)),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRouter.addCourse);
        },
        tooltip: 'Ajouter un cours',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: const InputDecoration(
          hintText: 'Rechercher un cours...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  Widget _buildFilterSummary(AppProvider appProvider) {
    final hasFilters = _selectedLevel != null ||
        _selectedCategory != null ||
        _selectedSubjectId != null;

    if (!hasFilters) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Wrap(
        spacing: 8.0,
        children: [
          if (_selectedLevel != null)
            Chip(
              label: Text(_getEducationLevelName(_selectedLevel!)),
              onDeleted: () {
                setState(() {
                  _selectedLevel = null;
                });
              },
            ),
          if (_selectedCategory != null)
            Chip(
              label: Text(_getCourseCategoryName(_selectedCategory!)),
              onDeleted: () {
                setState(() {
                  _selectedCategory = null;
                });
              },
            ),
          if (_selectedSubjectId != null)
            Chip(
              label: Text(appProvider.getSubject(_selectedSubjectId!)?.name ?? ''),
              onDeleted: () {
                setState(() {
                  _selectedSubjectId = null;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(AppProvider appProvider) {
    final filteredCourses = _getFilteredCourses(appProvider);
    final totalCourses = filteredCourses.length;
    final activeCourses = filteredCourses.where((c) => c.isActive).length;

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      totalCourses.toString(),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: AppTheme.primaryBlue,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Text('Cours total'),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      activeCourses.toString(),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: AppTheme.successGreen,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Text('Cours actifs'),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoursesList(AppProvider appProvider) {
    final filteredCourses = _getFilteredCourses(appProvider);

    if (filteredCourses.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun cours trouvé',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Ajoutez votre premier cours ou modifiez vos filtres',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: filteredCourses.length,
      itemBuilder: (context, index) {
        final course = filteredCourses[index];
        return CourseCard(
          course: course,
          subject: appProvider.getSubject(course.subjectId),
          onTap: () {
            Navigator.pushNamed(
              context,
              AppRouter.courseDetail,
              arguments: course.id,
            );
          },
        );
      },
    );
  }

  List<Course> _getFilteredCourses(AppProvider appProvider) {
    var courses = appProvider.activeCourses;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      courses = courses.where((course) {
        final subject = appProvider.getSubject(course.subjectId);
        return course.name.toLowerCase().contains(_searchQuery) ||
            course.code.toLowerCase().contains(_searchQuery) ||
            (subject?.name.toLowerCase().contains(_searchQuery) ?? false);
      }).toList();
    }

    // Apply level filter
    if (_selectedLevel != null) {
      courses = courses.where((course) => course.level == _selectedLevel).toList();
    }

    // Apply category filter
    if (_selectedCategory != null) {
      courses = courses.where((course) {
        final subject = appProvider.getSubject(course.subjectId);
        return subject?.category == _selectedCategory;
      }).toList();
    }

    // Apply subject filter
    if (_selectedSubjectId != null) {
      courses = courses.where((course) => course.subjectId == _selectedSubjectId).toList();
    }

    // Sort by name
    courses.sort((a, b) => a.name.compareTo(b.name));

    return courses;
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return AlertDialog(
            title: const Text('Filtrer les cours'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Niveau scolaire'),
                  DropdownButton<EducationLevel?>(
                    value: _selectedLevel,
                    isExpanded: true,
                    items: [
                      const DropdownMenuItem<EducationLevel?>(
                        value: null,
                        child: Text('Tous les niveaux'),
                      ),
                      ...EducationLevel.values.map((level) {
                        return DropdownMenuItem<EducationLevel?>(
                          value: level,
                          child: Text(_getEducationLevelName(level)),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedLevel = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text('Catégorie'),
                  DropdownButton<CourseCategory?>(
                    value: _selectedCategory,
                    isExpanded: true,
                    items: [
                      const DropdownMenuItem<CourseCategory?>(
                        value: null,
                        child: Text('Toutes les catégories'),
                      ),
                      ...CourseCategory.values.map((category) {
                        return DropdownMenuItem<CourseCategory?>(
                          value: category,
                          child: Text(_getCourseCategoryName(category)),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text('Matière'),
                  DropdownButton<String?>(
                    value: _selectedSubjectId,
                    isExpanded: true,
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('Toutes les matières'),
                      ),
                      ...appProvider.activeSubjects.map((subject) {
                        return DropdownMenuItem<String?>(
                          value: subject.id,
                          child: Text(subject.name),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedSubjectId = value;
                      });
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedLevel = null;
                    _selectedCategory = null;
                    _selectedSubjectId = null;
                  });
                  Navigator.pop(context);
                },
                child: const Text('Réinitialiser'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          );
        },
      ),
    );
  }

  String _getEducationLevelName(EducationLevel level) {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  String _getCourseCategoryName(CourseCategory category) {
    switch (category) {
      case CourseCategory.languesFrancaises:
        return 'Français';
      case CourseCategory.mathematiques:
        return 'Mathématiques';
      case CourseCategory.sciences:
        return 'Sciences';
      case CourseCategory.histoireGeographie:
        return 'Histoire-Géographie';
      case CourseCategory.languesVivantes:
        return 'Langues vivantes';
      case CourseCategory.arts:
        return 'Arts';
      case CourseCategory.eps:
        return 'EPS';
      case CourseCategory.technologie:
        return 'Technologie';
      case CourseCategory.educationCivique:
        return 'Éducation civique';
      case CourseCategory.philosophie:
        return 'Philosophie';
      case CourseCategory.specialites:
        return 'Spécialités';
      case CourseCategory.autre:
        return 'Autre';
    }
  }
}
