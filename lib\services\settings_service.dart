import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_settings.dart';

class SettingsService {
  static const String _settingsBoxName = 'settings';
  static const String _settingsKey = 'app_settings';
  
  static Box<AppSettings>? _settingsBox;
  static SharedPreferences? _prefs;
  
  // Initialize the service
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
    
    // Register Hive adapter if not already registered
    if (!Hive.isAdapterRegistered(10)) {
      Hive.registerAdapter(AppSettingsAdapter());
    }
    
    // Open settings box
    _settingsBox = await Hive.openBox<AppSettings>(_settingsBoxName);
  }
  
  // Get current settings
  static AppSettings get currentSettings {
    return _settingsBox?.get(_settingsKey) ?? AppSettings();
  }
  
  // Save settings
  static Future<void> saveSettings(AppSettings settings) async {
    await _settingsBox?.put(_settingsKey, settings);
  }
  
  // Update specific setting
  static Future<void> updateSetting<T>(String key, T value) async {
    final currentSettings = SettingsService.currentSettings;
    AppSettings updatedSettings;
    
    switch (key) {
      case 'isDarkMode':
        updatedSettings = currentSettings.copyWith(isDarkMode: value as bool);
        break;
      case 'language':
        updatedSettings = currentSettings.copyWith(language: value as String);
        break;
      case 'enableNotifications':
        updatedSettings = currentSettings.copyWith(enableNotifications: value as bool);
        break;
      case 'autoBackup':
        updatedSettings = currentSettings.copyWith(autoBackup: value as bool);
        break;
      case 'backupFrequency':
        updatedSettings = currentSettings.copyWith(backupFrequency: value as int);
        break;
      case 'showWelcomeScreen':
        updatedSettings = currentSettings.copyWith(showWelcomeScreen: value as bool);
        break;
      case 'dateFormat':
        updatedSettings = currentSettings.copyWith(dateFormat: value as String);
        break;
      case 'timeFormat':
        updatedSettings = currentSettings.copyWith(timeFormat: value as String);
        break;
      case 'enableSounds':
        updatedSettings = currentSettings.copyWith(enableSounds: value as bool);
        break;
      case 'enableVibration':
        updatedSettings = currentSettings.copyWith(enableVibration: value as bool);
        break;
      case 'fontSize':
        updatedSettings = currentSettings.copyWith(fontSize: value as double);
        break;
      case 'defaultGradingScale':
        updatedSettings = currentSettings.copyWith(defaultGradingScale: value as String);
        break;
      case 'showStudentPhotos':
        updatedSettings = currentSettings.copyWith(showStudentPhotos: value as bool);
        break;
      case 'enableQuickActions':
        updatedSettings = currentSettings.copyWith(enableQuickActions: value as bool);
        break;
      case 'enableGestures':
        updatedSettings = currentSettings.copyWith(enableGestures: value as bool);
        break;
      case 'exportFormat':
        updatedSettings = currentSettings.copyWith(exportFormat: value as String);
        break;
      case 'enableAnalytics':
        updatedSettings = currentSettings.copyWith(enableAnalytics: value as bool);
        break;
      case 'enableCrashReporting':
        updatedSettings = currentSettings.copyWith(enableCrashReporting: value as bool);
        break;
      case 'teacherName':
        updatedSettings = currentSettings.copyWith(teacherName: value as String);
        break;
      case 'schoolName':
        updatedSettings = currentSettings.copyWith(schoolName: value as String);
        break;
      case 'academicYear':
        updatedSettings = currentSettings.copyWith(academicYear: value as String);
        break;
      default:
        return; // Unknown setting, do nothing
    }
    
    await saveSettings(updatedSettings);
  }
  
  // Reset settings to default
  static Future<void> resetToDefaults() async {
    await saveSettings(AppSettings());
  }
  
  // Export settings to JSON
  static Map<String, dynamic> exportSettings() {
    return currentSettings.toJson();
  }
  
  // Import settings from JSON
  static Future<void> importSettings(Map<String, dynamic> json) async {
    final settings = AppSettings.fromJson(json);
    await saveSettings(settings);
  }
  
  // Get theme mode preference
  static bool get isDarkMode => currentSettings.isDarkMode;
  
  // Get language preference
  static String get language => currentSettings.language;
  
  // Get teacher info
  static String get teacherName => currentSettings.teacherName;
  static String get schoolName => currentSettings.schoolName;
  static String get academicYear => currentSettings.academicYear;
  
  // Get UI preferences
  static double get fontSize => currentSettings.fontSize;
  static bool get showStudentPhotos => currentSettings.showStudentPhotos;
  static bool get enableQuickActions => currentSettings.enableQuickActions;
  static bool get enableGestures => currentSettings.enableGestures;
  
  // Get notification preferences
  static bool get enableNotifications => currentSettings.enableNotifications;
  static bool get enableSounds => currentSettings.enableSounds;
  static bool get enableVibration => currentSettings.enableVibration;
  
  // Get backup preferences
  static bool get autoBackup => currentSettings.autoBackup;
  static int get backupFrequency => currentSettings.backupFrequency;
  
  // Get format preferences
  static String get dateFormat => currentSettings.dateFormat;
  static String get timeFormat => currentSettings.timeFormat;
  static String get exportFormat => currentSettings.exportFormat;
  static String get defaultGradingScale => currentSettings.defaultGradingScale;
  
  // Get privacy preferences
  static bool get enableAnalytics => currentSettings.enableAnalytics;
  static bool get enableCrashReporting => currentSettings.enableCrashReporting;
  
  // Get welcome screen preference
  static bool get showWelcomeScreen => currentSettings.showWelcomeScreen;
  
  // Close the service
  static Future<void> close() async {
    await _settingsBox?.close();
  }
  
  // Clear all settings (for testing or reset)
  static Future<void> clearAll() async {
    await _settingsBox?.clear();
  }
  
  // Check if settings exist
  static bool get hasSettings {
    return _settingsBox?.containsKey(_settingsKey) ?? false;
  }
  
  // Get settings box for listening to changes
  static Box<AppSettings>? get settingsBox => _settingsBox;
}
