import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/grade.dart';
import '../../models/student.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';
import '../../widgets/grade_card.dart';

class GradesScreen extends StatefulWidget {
  const GradesScreen({super.key});

  @override
  State<GradesScreen> createState() => _GradesScreenState();
}

class _GradesScreenState extends State<GradesScreen> {
  String _searchQuery = '';
  String? _selectedSubject;
  String? _selectedStudentId;
  GradeType? _selectedGradeType;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notes'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filtrer',
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showStatistics,
            tooltip: 'Statistiques',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              _buildFilterSummary(appProvider),
              _buildQuickStats(appProvider),
              Expanded(child: _buildGradesList(appProvider)),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(
            context,
            AppRouter.addGrade,
            arguments: <String, dynamic>{},
          );
        },
        icon: const Icon(Icons.add),
        label: const Text('Ajouter une note'),
      ),
    );
  }

  Widget _buildFilterSummary(AppProvider appProvider) {
    if (_searchQuery.isEmpty &&
        _selectedSubject == null &&
        _selectedStudentId == null &&
        _selectedGradeType == null) {
      return const SizedBox.shrink();
    }

    final selectedStudent = _selectedStudentId != null
        ? appProvider.getStudent(_selectedStudentId!)
        : null;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          if (_searchQuery.isNotEmpty)
            _buildFilterChip('Recherche: $_searchQuery', () {
              setState(() {
                _searchQuery = '';
              });
            }),
          if (_selectedSubject != null)
            _buildFilterChip('Matière: $_selectedSubject', () {
              setState(() {
                _selectedSubject = null;
              });
            }),
          if (selectedStudent != null)
            _buildFilterChip('Élève: ${selectedStudent.fullName}', () {
              setState(() {
                _selectedStudentId = null;
              });
            }),
          if (_selectedGradeType != null)
            _buildFilterChip(_getGradeTypeDisplayName(_selectedGradeType!), () {
              setState(() {
                _selectedGradeType = null;
              });
            }),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onRemove,
      backgroundColor: Theme.of(context).colorScheme.surface,
    );
  }

  Widget _buildQuickStats(AppProvider appProvider) {
    final filteredGrades = _getFilteredGrades(appProvider);

    if (filteredGrades.isEmpty) {
      return const SizedBox.shrink();
    }

    final averageGrade =
        filteredGrades.fold(0.0, (sum, grade) => sum + grade.normalizedValue) /
        filteredGrades.length;
    final excellentCount = filteredGrades
        .where((g) => g.normalizedValue >= 18)
        .length;
    final passableCount = filteredGrades
        .where((g) => g.normalizedValue >= 10)
        .length;
    final failingCount = filteredGrades
        .where((g) => g.normalizedValue < 10)
        .length;

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'Moyenne',
                '${averageGrade.toStringAsFixed(1)}/20',
                AppTheme.getGradeColor(averageGrade),
                Icons.trending_up,
              ),
              _buildStatItem(
                'Excellent',
                excellentCount.toString(),
                AppTheme.excellentGreen,
                Icons.star,
              ),
              _buildStatItem(
                'Réussite',
                passableCount.toString(),
                AppTheme.successGreen,
                Icons.check_circle,
              ),
              _buildStatItem(
                'Échec',
                failingCount.toString(),
                AppTheme.accentRed,
                Icons.cancel,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.getGradeColor(
                averageGrade,
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.getGradeColor(
                  averageGrade,
                ).withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              'Appréciation générale: ${_getAppreciation(averageGrade)}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.getGradeColor(averageGrade),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  Widget _buildGradesList(AppProvider appProvider) {
    final filteredGrades = _getFilteredGrades(appProvider);

    if (filteredGrades.isEmpty) {
      return _buildEmptyState();
    }

    // Group grades by subject and student
    final groupedGrades = <String, Map<String, List<Grade>>>{};
    for (final grade in filteredGrades) {
      groupedGrades.putIfAbsent(grade.subject, () => {});
      groupedGrades[grade.subject]!
          .putIfAbsent(grade.studentId, () => [])
          .add(grade);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: groupedGrades.length,
      itemBuilder: (context, index) {
        final subject = groupedGrades.keys.elementAt(index);
        final studentGrades = groupedGrades[subject]!;

        return _buildSubjectGroup(subject, studentGrades, appProvider);
      },
    );
  }

  Widget _buildSubjectGroup(
    String subject,
    Map<String, List<Grade>> studentGrades,
    AppProvider appProvider,
  ) {
    // Calculate subject average
    final allGrades = studentGrades.values.expand((grades) => grades).toList();
    final subjectAverage =
        allGrades.fold(0.0, (sum, grade) => sum + grade.normalizedValue) /
        allGrades.length;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subject header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    subject,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.getGradeColor(
                      subjectAverage,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppTheme.getGradeColor(subjectAverage),
                    ),
                  ),
                  child: Text(
                    'Moy: ${subjectAverage.toStringAsFixed(1)}/20',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getGradeColor(subjectAverage),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Student grades
          ...studentGrades.entries.map((entry) {
            final studentId = entry.key;
            final grades = entry.value;
            final student = appProvider.getStudent(studentId);

            return _buildStudentGradesSection(
              student,
              grades,
              subject,
              appProvider,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildStudentGradesSection(
    Student? student,
    List<Grade> grades,
    String subject,
    AppProvider appProvider,
  ) {
    if (student == null) return const SizedBox.shrink();

    // Sort grades by date (most recent first)
    grades.sort((a, b) => b.date.compareTo(a.date));

    // Calculate student average for this subject
    final studentAverage = appProvider.calculateStudentAverage(
      student.id,
      subject,
    );

    return ExpansionTile(
      leading: CircleAvatar(
        backgroundColor: AppTheme.getGradeColor(
          studentAverage,
        ).withValues(alpha: 0.1),
        child: Text(
          student.initials,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.getGradeColor(studentAverage),
          ),
        ),
      ),
      title: Text(
        student.fullName,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        'Moyenne: ${studentAverage.toStringAsFixed(1)}/20 - ${_getAppreciation(studentAverage)}',
        style: TextStyle(
          color: AppTheme.getGradeColor(studentAverage),
          fontWeight: FontWeight.w500,
        ),
      ),
      children: grades
          .map(
            (grade) => GradeCard(
              grade: grade,
              student: student,
              onEdit: () => _editGrade(grade),
              onDelete: () => _deleteGrade(grade, appProvider),
              showStudent: false,
            ),
          )
          .toList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.grade_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucune note',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Commencez par ajouter des notes',
            style: TextStyle(fontSize: 16, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'Utilisez le bouton + en bas à droite',
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrer les notes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Rechercher',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('Filtres avancés à implémenter'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showStatistics() {
    // Navigate to statistics screen (to be implemented)
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Statistiques détaillées - À implémenter')),
    );
  }

  List<Grade> _getFilteredGrades(AppProvider appProvider) {
    var grades = appProvider.grades;

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      grades = grades.where((grade) {
        final student = appProvider.getStudent(grade.studentId);
        return student?.fullName.toLowerCase().contains(_searchQuery) == true ||
            grade.subject.toLowerCase().contains(_searchQuery) ||
            grade.title.toLowerCase().contains(_searchQuery) ||
            grade.description?.toLowerCase().contains(_searchQuery) == true;
      }).toList();
    }

    // Filter by subject
    if (_selectedSubject != null) {
      grades = grades
          .where((grade) => grade.subject == _selectedSubject)
          .toList();
    }

    // Filter by student
    if (_selectedStudentId != null) {
      grades = grades
          .where((grade) => grade.studentId == _selectedStudentId)
          .toList();
    }

    // Filter by grade type
    if (_selectedGradeType != null) {
      grades = grades
          .where((grade) => grade.type == _selectedGradeType)
          .toList();
    }

    // Sort by date (most recent first)
    grades.sort((a, b) => b.date.compareTo(a.date));

    return grades;
  }

  String _getGradeTypeDisplayName(GradeType type) {
    switch (type) {
      case GradeType.evaluation:
        return 'Évaluation';
      case GradeType.homework:
        return 'Devoir';
      case GradeType.participation:
        return 'Participation';
      case GradeType.project:
        return 'Projet';
      case GradeType.exam:
        return 'Examen';
      case GradeType.quiz:
        return 'Interrogation';
    }
  }

  String _getAppreciation(double grade) {
    if (grade >= 18) return 'Excellent';
    if (grade >= 16) return 'Très bien';
    if (grade >= 14) return 'Bien';
    if (grade >= 12) return 'Assez bien';
    if (grade >= 10) return 'Passable';
    if (grade >= 8) return 'Insuffisant';
    return 'Très insuffisant';
  }

  void _editGrade(Grade grade) {
    Navigator.pushNamed(
      context,
      AppRouter.addGrade,
      arguments: {
        'gradeId': grade.id,
        'studentId': grade.studentId,
        'subject': grade.subject,
      },
    );
  }

  void _deleteGrade(Grade grade, AppProvider appProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la note'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer cette note ? '
          'Cette action ne peut pas être annulée.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await appProvider.deleteGrade(grade.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Note supprimée'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la suppression: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
