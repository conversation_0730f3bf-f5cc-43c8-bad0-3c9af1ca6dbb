import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/attendance.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class AttendanceHistoryScreen extends StatefulWidget {
  const AttendanceHistoryScreen({super.key});

  @override
  State<AttendanceHistoryScreen> createState() =>
      _AttendanceHistoryScreenState();
}

class _AttendanceHistoryScreenState extends State<AttendanceHistoryScreen> {
  final DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  final DateTime _endDate = DateTime.now();
  String? _selectedStudentId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Historique des présences'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filtrer',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return Column(
            children: [
              _buildFilterSummary(appProvider),
              Expanded(child: _buildAttendanceHistory(appProvider)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterSummary(AppProvider appProvider) {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final selectedStudent = _selectedStudentId != null
        ? appProvider.getStudent(_selectedStudentId!)
        : null;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Période: ${dateFormat.format(_startDate)} - ${dateFormat.format(_endDate)}',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          if (selectedStudent != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                'Élève: ${selectedStudent.fullName}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAttendanceHistory(AppProvider appProvider) {
    final attendanceRecords = _getFilteredAttendanceRecords(appProvider);

    if (attendanceRecords.isEmpty) {
      return _buildEmptyState();
    }

    // Group records by date
    final groupedRecords = <String, List<AttendanceRecord>>{};
    for (final record in attendanceRecords) {
      final dateKey = DateFormat('yyyy-MM-dd').format(record.date);
      groupedRecords.putIfAbsent(dateKey, () => []).add(record);
    }

    final sortedDates = groupedRecords.keys.toList()
      ..sort((a, b) => b.compareTo(a)); // Most recent first

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateKey = sortedDates[index];
        final date = DateTime.parse(dateKey);
        final records = groupedRecords[dateKey]!;

        return _buildDateGroup(date, records, appProvider);
      },
    );
  }

  Widget _buildDateGroup(
    DateTime date,
    List<AttendanceRecord> records,
    AppProvider appProvider,
  ) {
    final dateFormat = DateFormat('EEEE d MMMM yyyy', 'fr_FR');
    final isToday = _isSameDay(date, DateTime.now());

    // Calculate stats for this date
    final presentCount = records
        .where((r) => r.status == AttendanceStatus.present)
        .length;
    final absentCount = records
        .where((r) => r.status == AttendanceStatus.absent)
        .length;
    final lateCount = records
        .where((r) => r.status == AttendanceStatus.late)
        .length;
    final excusedCount = records
        .where((r) => r.status == AttendanceStatus.excused)
        .length;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    dateFormat.format(date),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (isToday)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.successGreen,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Aujourd\'hui',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRouter.dailyAttendance,
                      arguments: date,
                    );
                  },
                  tooltip: 'Modifier',
                ),
              ],
            ),
          ),

          // Stats summary
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatChip('Présents', presentCount, AppTheme.successGreen),
                _buildStatChip('Absents', absentCount, AppTheme.accentRed),
                _buildStatChip('Retards', lateCount, AppTheme.warningOrange),
                _buildStatChip('Excusés', excusedCount, AppTheme.secondaryBlue),
              ],
            ),
          ),

          // Individual records (if filtering by student)
          if (_selectedStudentId != null)
            ...records.map(
              (record) => _buildAttendanceRecordTile(record, appProvider),
            ),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(label, style: TextStyle(fontSize: 12, color: color)),
        ],
      ),
    );
  }

  Widget _buildAttendanceRecordTile(
    AttendanceRecord record,
    AppProvider appProvider,
  ) {
    final student = appProvider.getStudent(record.studentId);
    if (student == null) return const SizedBox.shrink();

    final color = AppTheme.getAttendanceColor(record.statusDisplayName);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withValues(alpha: 0.1),
        child: Icon(_getStatusIcon(record.status), color: color, size: 20),
      ),
      title: Text(student.fullName),
      subtitle: record.notes != null ? Text(record.notes!) : null,
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color),
        ),
        child: Text(
          record.statusDisplayName,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucune donnée de présence',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Aucune présence trouvée pour la période sélectionnée',
            style: TextStyle(fontSize: 16, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrer l\'historique'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Date range selection would go here
            const Text('Filtres avancés à implémenter'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {});
            },
            child: const Text('Appliquer'),
          ),
        ],
      ),
    );
  }

  List<AttendanceRecord> _getFilteredAttendanceRecords(
    AppProvider appProvider,
  ) {
    // This would get attendance records from the database
    // For now, return empty list as we need to implement database queries
    return [];
  }

  IconData _getStatusIcon(AttendanceStatus status) {
    switch (status) {
      case AttendanceStatus.present:
        return Icons.check_circle;
      case AttendanceStatus.absent:
        return Icons.cancel;
      case AttendanceStatus.late:
        return Icons.access_time;
      case AttendanceStatus.excused:
        return Icons.info;
      case AttendanceStatus.sick:
        return Icons.local_hospital;
    }
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}
