import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/lesson.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class TimetableScreen extends StatefulWidget {
  const TimetableScreen({super.key});

  @override
  State<TimetableScreen> createState() => _TimetableScreenState();
}

class _TimetableScreenState extends State<TimetableScreen> {
  DateTime _selectedWeek = DateTime.now();
  final ScrollController _scrollController = ScrollController();
  final ScrollController _timeScrollController = ScrollController();

  // Time slots from 7:00 to 19:00 (7 AM to 7 PM)
  static const int startHour = 7;
  static const int endHour = 19;
  static const int totalHours = endHour - startHour;
  static const double hourHeight = 80.0;
  static const double dayWidth = 120.0;

  @override
  void initState() {
    super.initState();
    _selectedWeek = _getStartOfWeek(DateTime.now());

    // Auto-scroll to current time
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentTime();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _timeScrollController.dispose();
    super.dispose();
  }

  DateTime _getStartOfWeek(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  void _scrollToCurrentTime() {
    final now = DateTime.now();
    if (now.hour >= startHour && now.hour < endHour) {
      final scrollOffset =
          (now.hour - startHour) * hourHeight +
          (now.minute / 60) * hourHeight -
          200;
      _timeScrollController.animateTo(
        scrollOffset.clamp(0.0, _timeScrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Emploi du temps'),
        actions: [
          IconButton(
            icon: const Icon(Icons.today),
            onPressed: () {
              setState(() {
                _selectedWeek = _getStartOfWeek(DateTime.now());
              });
              _scrollToCurrentTime();
            },
            tooltip: 'Aujourd\'hui',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.addLesson);
            },
            tooltip: 'Ajouter un cours',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              _buildWeekSelector(),
              _buildDayHeaders(),
              Expanded(child: _buildTimetableGrid(appProvider)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildWeekSelector() {
    final weekStart = _selectedWeek;
    final weekEnd = weekStart.add(const Duration(days: 6));

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: () {
              setState(() {
                _selectedWeek = _selectedWeek.subtract(const Duration(days: 7));
              });
            },
          ),
          Expanded(
            child: Text(
              '${DateFormat('d MMM').format(weekStart)} - ${DateFormat('d MMM yyyy').format(weekEnd)}',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: () {
              setState(() {
                _selectedWeek = _selectedWeek.add(const Duration(days: 7));
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDayHeaders() {
    final days = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
    final today = DateTime.now();

    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
      ),
      child: Row(
        children: [
          // Time column header
          Container(
            width: 60,
            alignment: Alignment.center,
            child: const Text(
              'Heure',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          // Day headers - use Expanded to prevent overflow
          Expanded(
            child: Row(
              children: List.generate(7, (index) {
                final date = _selectedWeek.add(Duration(days: index));
                final isToday =
                    date.day == today.day &&
                    date.month == today.month &&
                    date.year == today.year;

                return Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: isToday
                          ? AppTheme.primaryBlue.withValues(alpha: 0.1)
                          : null,
                      border: Border(
                        left: index > 0
                            ? BorderSide(
                                color: Colors.grey.withValues(alpha: 0.3),
                              )
                            : BorderSide.none,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          days[index],
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: isToday ? AppTheme.primaryBlue : null,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          DateFormat('d').format(date),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isToday ? AppTheme.primaryBlue : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimetableGrid(AppProvider appProvider) {
    final weekLessons = _getWeekLessons(appProvider.lessons);

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive day width
        final availableWidth =
            constraints.maxWidth - 60; // Subtract time column width
        final responsiveDayWidth = availableWidth / 7;
        final minDayWidth = 80.0; // Minimum width for readability
        final actualDayWidth = responsiveDayWidth < minDayWidth
            ? minDayWidth
            : responsiveDayWidth;
        final totalGridWidth = actualDayWidth * 7;

        return SingleChildScrollView(
          controller: _timeScrollController,
          child: SizedBox(
            height: totalHours * hourHeight,
            child: Row(
              children: [
                // Time column
                _buildTimeColumn(),
                // Days columns
                Expanded(
                  child: actualDayWidth == responsiveDayWidth
                      ? _buildResponsiveGrid(weekLessons, actualDayWidth)
                      : SingleChildScrollView(
                          controller: _scrollController,
                          scrollDirection: Axis.horizontal,
                          child: SizedBox(
                            width: totalGridWidth,
                            child: _buildFixedGrid(weekLessons, actualDayWidth),
                          ),
                        ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildResponsiveGrid(List<List<Lesson>> weekLessons, double dayWidth) {
    return Stack(
      children: [
        // Grid lines
        _buildGridLines(dayWidth),
        // Current time indicator
        _buildCurrentTimeIndicator(dayWidth),
        // Lessons
        ...List.generate(7, (dayIndex) {
          final date = _selectedWeek.add(Duration(days: dayIndex));
          final dayLessons = weekLessons[dayIndex];

          return Positioned(
            left: dayIndex * dayWidth,
            child: SizedBox(
              width: dayWidth,
              height: totalHours * hourHeight,
              child: Stack(
                children: dayLessons.map((lesson) {
                  return _buildLessonCard(lesson, date);
                }).toList(),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildFixedGrid(List<List<Lesson>> weekLessons, double dayWidth) {
    return Stack(
      children: [
        // Grid lines
        _buildGridLines(dayWidth),
        // Current time indicator
        _buildCurrentTimeIndicator(dayWidth),
        // Lessons
        ...List.generate(7, (dayIndex) {
          final date = _selectedWeek.add(Duration(days: dayIndex));
          final dayLessons = weekLessons[dayIndex];

          return Positioned(
            left: dayIndex * dayWidth,
            child: SizedBox(
              width: dayWidth,
              height: totalHours * hourHeight,
              child: Stack(
                children: dayLessons.map((lesson) {
                  return _buildLessonCard(lesson, date);
                }).toList(),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildTimeColumn() {
    return Container(
      width: 60,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          right: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
      ),
      child: Column(
        children: List.generate(totalHours, (index) {
          final hour = startHour + index;
          return Container(
            height: hourHeight,
            alignment: Alignment.topCenter,
            padding: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
              ),
            ),
            child: Text(
              '${hour.toString().padLeft(2, '0')}:00',
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildGridLines([double? customDayWidth]) {
    final actualDayWidth = customDayWidth ?? dayWidth;
    return Column(
      children: List.generate(totalHours, (index) {
        return Container(
          height: hourHeight,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
            ),
          ),
          child: Row(
            children: List.generate(7, (dayIndex) {
              return Container(
                width: actualDayWidth,
                decoration: BoxDecoration(
                  border: Border(
                    left: dayIndex > 0
                        ? BorderSide(color: Colors.grey.withValues(alpha: 0.2))
                        : BorderSide.none,
                  ),
                ),
              );
            }),
          ),
        );
      }),
    );
  }

  Widget _buildCurrentTimeIndicator([double? customDayWidth]) {
    final actualDayWidth = customDayWidth ?? dayWidth;
    final now = DateTime.now();
    final today = DateTime.now();

    // Only show if current week and within time range
    if (!_isDateInCurrentWeek(today) ||
        now.hour < startHour ||
        now.hour >= endHour) {
      return const SizedBox.shrink();
    }

    final dayIndex = today.weekday - 1; // Monday = 0
    final hourOffset = now.hour - startHour;
    final minuteOffset = now.minute / 60;
    final topPosition = (hourOffset + minuteOffset) * hourHeight;

    return Positioned(
      top: topPosition,
      left: dayIndex * actualDayWidth,
      child: Container(
        width: actualDayWidth,
        height: 2,
        color: Colors.red,
        child: Container(
          width: 8,
          height: 8,
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  bool _isDateInCurrentWeek(DateTime date) {
    final weekStart = _selectedWeek;
    final weekEnd = weekStart.add(const Duration(days: 6));
    return date.isAfter(weekStart.subtract(const Duration(days: 1))) &&
        date.isBefore(weekEnd.add(const Duration(days: 1)));
  }

  List<List<Lesson>> _getWeekLessons(List<Lesson> allLessons) {
    final weekLessons = List.generate(7, (index) => <Lesson>[]);

    for (final lesson in allLessons) {
      final lessonDate = lesson.startTime;
      final weekStart = _selectedWeek;
      final weekEnd = weekStart.add(const Duration(days: 6));

      // Check if lesson is in current week
      if (lessonDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
          lessonDate.isBefore(weekEnd.add(const Duration(days: 1)))) {
        final dayIndex = lessonDate.weekday - 1; // Monday = 0
        weekLessons[dayIndex].add(lesson);
      }
    }

    // Sort lessons by start time for each day
    for (final dayLessons in weekLessons) {
      dayLessons.sort((a, b) => a.startTime.compareTo(b.startTime));
    }

    return weekLessons;
  }

  Widget _buildLessonCard(Lesson lesson, DateTime date) {
    final startHourOffset = lesson.startTime.hour - startHour;
    final startMinuteOffset = lesson.startTime.minute / 60;
    final topPosition = (startHourOffset + startMinuteOffset) * hourHeight;

    final duration = lesson.endTime.difference(lesson.startTime);
    final height = (duration.inMinutes / 60) * hourHeight;

    return Positioned(
      top: topPosition,
      left: 2,
      right: 2,
      child: GestureDetector(
        onTap: () {
          Navigator.pushNamed(
            context,
            AppRouter.lessonDetail,
            arguments: lesson.id,
          );
        },
        child: Container(
          height: height - 2,
          margin: const EdgeInsets.only(bottom: 2),
          decoration: BoxDecoration(
            color: _getLessonColor(lesson.status),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: _getLessonBorderColor(lesson.status),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  lesson.title,
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (height > 40) ...[
                  const SizedBox(height: 2),
                  Text(
                    lesson.subject,
                    style: const TextStyle(fontSize: 10, color: Colors.white70),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                if (height > 60 && lesson.classroom != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    lesson.classroom!,
                    style: const TextStyle(fontSize: 9, color: Colors.white60),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getLessonColor(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return AppTheme.primaryBlue;
      case LessonStatus.inProgress:
        return AppTheme.warningOrange;
      case LessonStatus.completed:
        return AppTheme.successGreen;
      case LessonStatus.cancelled:
        return AppTheme.accentRed;
    }
  }

  Color _getLessonBorderColor(LessonStatus status) {
    return _getLessonColor(status).withValues(alpha: 0.8);
  }
}
