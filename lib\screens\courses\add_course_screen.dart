import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';

class AddCourseScreen extends StatefulWidget {
  const AddCourseScreen({super.key});

  @override
  State<AddCourseScreen> createState() => _AddCourseScreenState();
}

class _AddCourseScreenState extends State<AddCourseScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _estimatedHoursController = TextEditingController();
  final _academicYearController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedSubjectId;
  EducationLevel _selectedLevel = EducationLevel.cp;
  CourseDifficulty _selectedDifficulty = CourseDifficulty.intermediaire;
  final List<LearningObjective> _objectives = [];

  @override
  void initState() {
    super.initState();
    _estimatedHoursController.text = '0';
    _academicYearController.text =
        '${DateTime.now().year}-${DateTime.now().year + 1}';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    _estimatedHoursController.dispose();
    _academicYearController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ajouter un cours'),
        actions: [
          TextButton(onPressed: _saveCourse, child: const Text('Enregistrer')),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBasicInfoSection(appProvider),
                  const SizedBox(height: 24),
                  _buildDetailsSection(),
                  const SizedBox(height: 24),
                  _buildObjectivesSection(),
                  const SizedBox(height: 24),
                  _buildNotesSection(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBasicInfoSection(AppProvider appProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de base',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom du cours *',
                prefixIcon: Icon(Icons.school),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom du cours est requis';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Code du cours *',
                prefixIcon: Icon(Icons.tag),
                hintText: 'Ex: FR-CP-2024',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le code du cours est requis';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            ClipRect(
              child: DropdownButtonFormField<String>(
                value: _selectedSubjectId,
                isExpanded: true,
                decoration: const InputDecoration(
                  labelText: 'Matière *',
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: appProvider.activeSubjects.map((subject) {
                  return DropdownMenuItem(
                    value: subject.id,
                    child: Text(subject.name, overflow: TextOverflow.ellipsis),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedSubjectId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Veuillez sélectionner une matière';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<EducationLevel>(
              value: _selectedLevel,
              decoration: const InputDecoration(
                labelText: 'Niveau scolaire *',
                prefixIcon: Icon(Icons.grade),
              ),
              items: EducationLevel.values.map((level) {
                return DropdownMenuItem(
                  value: level,
                  child: Text(_getEducationLevelName(level)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedLevel = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Détails du cours',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CourseDifficulty>(
              value: _selectedDifficulty,
              isExpanded: true,
              decoration: const InputDecoration(
                labelText: 'Niveau de difficulté',
                prefixIcon: Icon(Icons.trending_up),
              ),
              items: CourseDifficulty.values.map((difficulty) {
                return DropdownMenuItem(
                  value: difficulty,
                  child: Text(_getDifficultyName(difficulty)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedDifficulty = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _estimatedHoursController,
              decoration: const InputDecoration(
                labelText: 'Heures estimées',
                prefixIcon: Icon(Icons.access_time),
                suffixText: 'heures',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final hours = int.tryParse(value);
                  if (hours == null || hours < 0) {
                    return 'Veuillez entrer un nombre valide';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _academicYearController,
              decoration: const InputDecoration(
                labelText: 'Année scolaire',
                prefixIcon: Icon(Icons.calendar_today),
                hintText: '2024-2025',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectivesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Objectifs pédagogiques',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: _addObjective,
                  icon: const Icon(Icons.add),
                  label: const Text('Ajouter'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_objectives.isEmpty)
              const Text(
                'Aucun objectif défini. Ajoutez des objectifs pour structurer votre cours.',
                style: TextStyle(color: Colors.grey),
              )
            else
              ..._objectives.asMap().entries.map((entry) {
                final index = entry.key;
                final objective = entry.value;
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    title: Text(objective.title),
                    subtitle: objective.description != null
                        ? Text(objective.description!)
                        : null,
                    trailing: IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _removeObjective(index),
                    ),
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes personnelles',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                prefixIcon: Icon(Icons.note),
                hintText: 'Notes personnelles sur ce cours...',
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  void _addObjective() {
    showDialog(
      context: context,
      builder: (context) {
        final titleController = TextEditingController();
        final descriptionController = TextEditingController();

        return AlertDialog(
          title: const Text('Ajouter un objectif'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: titleController,
                decoration: const InputDecoration(
                  labelText: 'Titre de l\'objectif *',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (optionnel)',
                ),
                maxLines: 2,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () {
                if (titleController.text.trim().isNotEmpty) {
                  setState(() {
                    _objectives.add(
                      LearningObjective(
                        title: titleController.text.trim(),
                        description: descriptionController.text.trim().isEmpty
                            ? null
                            : descriptionController.text.trim(),
                        order: _objectives.length,
                      ),
                    );
                  });
                  Navigator.pop(context);
                }
              },
              child: const Text('Ajouter'),
            ),
          ],
        );
      },
    );
  }

  void _removeObjective(int index) {
    setState(() {
      _objectives.removeAt(index);
      // Update order for remaining objectives
      for (int i = 0; i < _objectives.length; i++) {
        _objectives[i].order = i;
      }
    });
  }

  void _saveCourse() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final appProvider = context.read<AppProvider>();

    // Check license restrictions for course limit
    if (!appProvider.isLicensed) {
      final currentCourseCount = appProvider.courses
          .where((c) => c.isActive)
          .length;
      if (currentCourseCount >= 5) {
        appProvider.showRestrictionDialog(context, 'unlimited_courses');
        return;
      }
    }

    try {
      final course = Course(
        name: _nameController.text.trim(),
        code: _codeController.text.trim(),
        subjectId: _selectedSubjectId!,
        level: _selectedLevel,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        objectives: _objectives.isEmpty ? null : _objectives,
        difficulty: _selectedDifficulty,
        estimatedHours: int.tryParse(_estimatedHoursController.text) ?? 0,
        academicYear: _academicYearController.text.trim().isEmpty
            ? null
            : _academicYearController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      await appProvider.addCourse(course);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cours ajouté avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  String _getEducationLevelName(EducationLevel level) {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  String _getDifficultyName(CourseDifficulty difficulty) {
    switch (difficulty) {
      case CourseDifficulty.debutant:
        return 'Débutant';
      case CourseDifficulty.intermediaire:
        return 'Intermédiaire';
      case CourseDifficulty.avance:
        return 'Avancé';
      case CourseDifficulty.expert:
        return 'Expert';
    }
  }
}
