import 'package:flutter/material.dart';
import '../utils/app_theme.dart';
import '../utils/app_router.dart';

class QuickActionGrid extends StatelessWidget {
  const QuickActionGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Actions rapides',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            _buildQuickActionCard(
              context,
              icon: Icons.people,
              title: 'Élèves',
              subtitle: 'Gérer les élèves',
              color: AppTheme.primaryGreen,
              onTap: () => Navigator.pushNamed(context, AppRouter.students),
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.school,
              title: 'Cours',
              subtitle: '<PERSON><PERSON>rer les cours',
              color: Colors.indigo,
              onTap: () => Navigator.pushNamed(context, AppRouter.courses),
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.check_circle,
              title: 'Présences',
              subtitle: 'Appel rapide',
              color: AppTheme.successGreen,
              onTap: () => Navigator.pushNamed(context, AppRouter.attendance),
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.grade,
              title: 'Notes',
              subtitle: 'Saisir les notes',
              color: AppTheme.warningOrange,
              onTap: () => Navigator.pushNamed(context, AppRouter.grades),
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.note,
              title: 'Comportement',
              subtitle: 'Notes rapides',
              color: AppTheme.goodPurple,
              onTap: () =>
                  Navigator.pushNamed(context, AppRouter.behaviorNotes),
            ),
            _buildQuickActionCard(
              context,
              icon: Icons.subject,
              title: 'Matières',
              subtitle: 'Gérer les matières',
              color: Colors.teal,
              onTap: () => Navigator.pushNamed(context, AppRouter.subjects),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(icon, size: 32, color: color),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
