import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';

class EditCourseScreen extends StatefulWidget {
  final String courseId;

  const EditCourseScreen({super.key, required this.courseId});

  @override
  State<EditCourseScreen> createState() => _EditCourseScreenState();
}

class _EditCourseScreenState extends State<EditCourseScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _estimatedHoursController = TextEditingController();
  final _academicYearController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedSubjectId;
  EducationLevel _selectedLevel = EducationLevel.cp;
  CourseDifficulty _selectedDifficulty = CourseDifficulty.intermediaire;
  List<LearningObjective> _objectives = [];
  bool _isActive = true;
  Course? _course;

  @override
  void initState() {
    super.initState();
    _loadCourse();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    _estimatedHoursController.dispose();
    _academicYearController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _loadCourse() {
    final appProvider = context.read<AppProvider>();
    _course = appProvider.getCourse(widget.courseId);

    if (_course != null) {
      _nameController.text = _course!.name;
      _codeController.text = _course!.code;
      _descriptionController.text = _course!.description ?? '';
      _estimatedHoursController.text = _course!.estimatedHours.toString();
      _academicYearController.text = _course!.academicYear ?? '';
      _notesController.text = _course!.notes ?? '';
      _selectedSubjectId = _course!.subjectId;
      _selectedLevel = _course!.level;
      _selectedDifficulty = _course!.difficulty;
      _objectives = List.from(_course!.objectives ?? []);
      _isActive = _course!.isActive;
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_course == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Cours introuvable')),
        body: const Center(
          child: Text('Ce cours n\'existe pas ou a été supprimé.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Modifier le cours'),
        actions: [
          TextButton(onPressed: _saveCourse, child: const Text('Enregistrer')),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBasicInfoSection(appProvider),
                  const SizedBox(height: 24),
                  _buildDetailsSection(),
                  const SizedBox(height: 24),
                  _buildObjectivesSection(),
                  const SizedBox(height: 24),
                  _buildNotesSection(),
                  const SizedBox(height: 24),
                  _buildStatusSection(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBasicInfoSection(AppProvider appProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de base',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom du cours *',
                isDense: true,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Veuillez saisir un nom de cours';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Code du cours *',
                isDense: true,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Veuillez saisir un code de cours';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            ClipRect(
              child: DropdownButtonFormField<String>(
                value: _selectedSubjectId,
                isExpanded: true,
                decoration: const InputDecoration(
                  labelText: 'Matière *',
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                ),
                items: appProvider.activeSubjects.map((subject) {
                  return DropdownMenuItem(
                    value: subject.id,
                    child: Text(subject.name, overflow: TextOverflow.ellipsis),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedSubjectId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Veuillez sélectionner une matière';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<EducationLevel>(
              value: _selectedLevel,
              decoration: const InputDecoration(
                labelText: 'Niveau *',
                isDense: true,
              ),
              items: EducationLevel.values.map((level) {
                return DropdownMenuItem(
                  value: level,
                  child: Text(_getLevelName(level)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedLevel = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                isDense: true,
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Détails du cours',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CourseDifficulty>(
              value: _selectedDifficulty,
              decoration: const InputDecoration(
                labelText: 'Difficulté',
                isDense: true,
              ),
              items: CourseDifficulty.values.map((difficulty) {
                return DropdownMenuItem(
                  value: difficulty,
                  child: Text(_getDifficultyName(difficulty)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedDifficulty = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _estimatedHoursController,
              decoration: const InputDecoration(
                labelText: 'Heures estimées',
                isDense: true,
                suffixText: 'heures',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final hours = int.tryParse(value);
                  if (hours == null || hours <= 0) {
                    return 'Veuillez saisir un nombre d\'heures valide';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _academicYearController,
              decoration: const InputDecoration(
                labelText: 'Année scolaire',
                isDense: true,
                hintText: 'Ex: 2023-2024',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectivesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Objectifs d\'apprentissage',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextButton.icon(
                  onPressed: _addObjective,
                  icon: const Icon(Icons.add),
                  label: const Text('Ajouter'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_objectives.isEmpty)
              const Text('Aucun objectif défini.')
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _objectives.length,
                itemBuilder: (context, index) {
                  final objective = _objectives[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(objective.title),
                      subtitle: objective.description != null
                          ? Text(objective.description!)
                          : null,
                      trailing: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () => _removeObjective(index),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes additionnelles',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                isDense: true,
                hintText: 'Notes ou commentaires sur le cours...',
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statut du cours',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Cours actif'),
              subtitle: const Text(
                'Le cours est disponible pour les étudiants',
              ),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _addObjective() {
    showDialog(
      context: context,
      builder: (context) => _ObjectiveDialog(
        onSave: (objective) {
          setState(() {
            _objectives.add(objective);
          });
        },
      ),
    );
  }

  void _removeObjective(int index) {
    setState(() {
      _objectives.removeAt(index);
    });
  }

  String _getLevelName(EducationLevel level) {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  String _getDifficultyName(CourseDifficulty difficulty) {
    switch (difficulty) {
      case CourseDifficulty.debutant:
        return 'Débutant';
      case CourseDifficulty.intermediaire:
        return 'Intermédiaire';
      case CourseDifficulty.avance:
        return 'Avancé';
      case CourseDifficulty.expert:
        return 'Expert';
    }
  }

  void _saveCourse() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedSubjectId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner une matière'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final updatedCourse = _course!.copyWith(
        name: _nameController.text.trim(),
        code: _codeController.text.trim().toUpperCase(),
        subjectId: _selectedSubjectId!,
        level: _selectedLevel,
        difficulty: _selectedDifficulty,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        estimatedHours: _estimatedHoursController.text.isEmpty
            ? null
            : int.tryParse(_estimatedHoursController.text),
        academicYear: _academicYearController.text.trim().isEmpty
            ? null
            : _academicYearController.text.trim(),
        objectives: _objectives,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isActive: _isActive,
      );

      await context.read<AppProvider>().updateCourse(updatedCourse);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cours modifié avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}

class _ObjectiveDialog extends StatefulWidget {
  final Function(LearningObjective) onSave;

  const _ObjectiveDialog({required this.onSave});

  @override
  State<_ObjectiveDialog> createState() => _ObjectiveDialogState();
}

class _ObjectiveDialogState extends State<_ObjectiveDialog> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Ajouter un objectif'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: 'Titre de l\'objectif *',
              isDense: true,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              isDense: true,
            ),
            maxLines: 2,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        TextButton(
          onPressed: () {
            if (_titleController.text.trim().isNotEmpty) {
              final objective = LearningObjective(
                title: _titleController.text.trim(),
                description: _descriptionController.text.trim().isEmpty
                    ? null
                    : _descriptionController.text.trim(),
              );
              widget.onSave(objective);
              Navigator.pop(context);
            }
          },
          child: const Text('Ajouter'),
        ),
      ],
    );
  }
}
