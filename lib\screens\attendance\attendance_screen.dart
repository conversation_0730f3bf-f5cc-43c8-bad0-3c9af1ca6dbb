import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/student.dart';
import '../../models/attendance.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';
import '../../widgets/attendance_student_card.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  DailyAttendance? _dailyAttendance;

  @override
  void initState() {
    super.initState();
    _loadAttendance();
  }

  Future<void> _loadAttendance() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final appProvider = context.read<AppProvider>();
      _dailyAttendance = await appProvider.getDailyAttendance(_selectedDate);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Présences'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.attendanceHistory);
            },
            tooltip: 'Historique',
          ),
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: 'Changer la date',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildDateHeader(),
          _buildAttendanceStats(),
          Expanded(child: _buildStudentsList()),
        ],
      ),
      floatingActionButton:
          _dailyAttendance != null && !_dailyAttendance!.isCompleted
          ? FloatingActionButton.extended(
              onPressed: _markAttendanceComplete,
              icon: const Icon(Icons.check),
              label: const Text('Terminer'),
              tooltip: 'Marquer l\'appel comme terminé',
            )
          : null,
    );
  }

  Widget _buildDateHeader() {
    final isToday = _isSameDay(_selectedDate, DateTime.now());
    final dateFormat = DateFormat('EEEE d MMMM yyyy');

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  dateFormat.format(_selectedDate),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (isToday)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.successGreen,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Aujourd\'hui',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          if (_dailyAttendance?.isCompleted == true)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppTheme.successGreen,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Appel terminé',
                    style: TextStyle(
                      color: AppTheme.successGreen,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAttendanceStats() {
    if (_dailyAttendance == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'Présents',
            _dailyAttendance!.presentCount.toString(),
            AppTheme.successGreen,
            Icons.check_circle,
          ),
          _buildStatItem(
            'Absents',
            _dailyAttendance!.absentCount.toString(),
            AppTheme.accentRed,
            Icons.cancel,
          ),
          _buildStatItem(
            'En retard',
            _dailyAttendance!.lateCount.toString(),
            AppTheme.warningOrange,
            Icons.access_time,
          ),
          _buildStatItem(
            'Excusés',
            _dailyAttendance!.excusedCount.toString(),
            AppTheme.secondaryBlue,
            Icons.info,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  Widget _buildStudentsList() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        if (_isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final students = appProvider.activeStudents;
        if (students.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: students.length,
          itemBuilder: (context, index) {
            final student = students[index];
            final attendanceRecord = _dailyAttendance?.getRecordForStudent(
              student.id,
            );

            return AttendanceStudentCard(
              student: student,
              attendanceRecord: attendanceRecord,
              onStatusChanged: (status) => _updateAttendance(student, status),
              isCompleted: _dailyAttendance?.isCompleted ?? false,
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucun élève',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ajoutez des élèves pour commencer l\'appel',
            style: TextStyle(fontSize: 16, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.students);
            },
            icon: const Icon(Icons.people),
            label: const Text('Gérer les élèves'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null && !_isSameDay(date, _selectedDate)) {
      setState(() {
        _selectedDate = date;
      });
      await _loadAttendance();
    }
  }

  Future<void> _updateAttendance(
    Student student,
    AttendanceStatus status,
  ) async {
    try {
      final appProvider = context.read<AppProvider>();
      await appProvider.updateAttendance(student.id, _selectedDate, status);

      // Reload attendance to get updated stats
      _dailyAttendance = await appProvider.getDailyAttendance(_selectedDate);
      setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la mise à jour: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  Future<void> _markAttendanceComplete() async {
    if (_dailyAttendance == null) return;

    try {
      _dailyAttendance!.isCompleted = true;
      final appProvider = context.read<AppProvider>();
      await appProvider.saveDailyAttendance(_dailyAttendance!);

      setState(() {});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Appel marqué comme terminé'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}
