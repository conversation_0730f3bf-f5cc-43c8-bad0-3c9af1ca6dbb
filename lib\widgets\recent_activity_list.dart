import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/app_provider.dart';
import '../models/behavior_note.dart';
import '../utils/app_theme.dart';

class RecentActivityList extends StatelessWidget {
  const RecentActivityList({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final recentActivities = _getRecentActivities(appProvider);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Activité récente',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () => _showAllActivities(context),
                  child: const Text('Voir tout'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (recentActivities.isEmpty)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    children: [
                      Icon(Icons.history, size: 48, color: Colors.grey[400]),
                      const SizedBox(height: 12),
                      Text(
                        'Aucune activité récente',
                        style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Les dernières actions apparaîtront ici',
                        style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                      ),
                    ],
                  ),
                ),
              )
            else
              Card(
                child: Column(
                  children: recentActivities
                      .take(5)
                      .map(
                        (activity) =>
                            _buildActivityItem(context, activity, appProvider),
                      )
                      .toList(),
                ),
              ),
          ],
        );
      },
    );
  }

  List<ActivityItem> _getRecentActivities(AppProvider appProvider) {
    final activities = <ActivityItem>[];

    // Add recent grades
    final recentGrades = appProvider.grades
        .where(
          (grade) => grade.createdAt.isAfter(
            DateTime.now().subtract(const Duration(days: 7)),
          ),
        )
        .toList();

    for (final grade in recentGrades) {
      final student = appProvider.getStudent(grade.studentId);
      if (student != null) {
        activities.add(
          ActivityItem(
            type: ActivityType.grade,
            title: 'Note ajoutée: ${grade.title}',
            subtitle: '${student.fullName} - ${grade.displayValue}',
            timestamp: grade.createdAt,
            icon: Icons.grade,
            color: AppTheme.getGradeColor(grade.normalizedValue),
            onTap: () => {}, // Navigate to grade detail
          ),
        );
      }
    }

    // Add recent behavior notes
    final recentBehaviorNotes = appProvider.behaviorNotes
        .where(
          (note) => note.createdAt.isAfter(
            DateTime.now().subtract(const Duration(days: 7)),
          ),
        )
        .toList();

    for (final note in recentBehaviorNotes) {
      final student = appProvider.getStudent(note.studentId);
      if (student != null) {
        activities.add(
          ActivityItem(
            type: ActivityType.behavior,
            title: 'Note de comportement',
            subtitle: '${student.fullName} - ${note.typeDisplayName}',
            timestamp: note.createdAt,
            icon: _getBehaviorIcon(note.type),
            color: _getBehaviorColor(note.type),
            onTap: () => {}, // Navigate to behavior note detail
          ),
        );
      }
    }

    // Add recent students
    final recentStudents = appProvider.students
        .where(
          (student) => student.createdAt.isAfter(
            DateTime.now().subtract(const Duration(days: 7)),
          ),
        )
        .toList();

    for (final student in recentStudents) {
      activities.add(
        ActivityItem(
          type: ActivityType.student,
          title: 'Nouvel élève ajouté',
          subtitle: student.fullName,
          timestamp: student.createdAt,
          icon: Icons.person_add,
          color: AppTheme.primaryGreen,
          onTap: () => {}, // Navigate to student detail
        ),
      );
    }

    // Sort by timestamp (most recent first)
    activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return activities;
  }

  Widget _buildActivityItem(
    BuildContext context,
    ActivityItem activity,
    AppProvider appProvider,
  ) {
    final timeAgo = _getTimeAgo(activity.timestamp);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: activity.color.withValues(alpha: 0.1),
        child: Icon(activity.icon, color: activity.color, size: 20),
      ),
      title: Text(
        activity.title,
        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(activity.subtitle, style: const TextStyle(fontSize: 13)),
          Text(
            timeAgo,
            style: TextStyle(fontSize: 12, color: Colors.grey[500]),
          ),
        ],
      ),
      onTap: activity.onTap,
      dense: true,
    );
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'À l\'instant';
    } else if (difference.inMinutes < 60) {
      return 'Il y a ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'Il y a ${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return 'Il y a ${difference.inDays}j';
    } else {
      return DateFormat('dd/MM/yyyy').format(timestamp);
    }
  }

  Color _getBehaviorColor(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return AppTheme.successGreen;
      case BehaviorType.negative:
        return AppTheme.accentRed;
      case BehaviorType.neutral:
        return AppTheme.neutralGray;
    }
  }

  IconData _getBehaviorIcon(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return Icons.thumb_up;
      case BehaviorType.negative:
        return Icons.thumb_down;
      case BehaviorType.neutral:
        return Icons.remove;
    }
  }

  void _showAllActivities(BuildContext context) {
    // Show a modal bottom sheet with all recent activities
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Historique complet',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: Consumer<AppProvider>(
                    builder: (modalContext, appProvider, child) {
                      final allActivities = _getAllActivities(
                        modalContext,
                        appProvider,
                      );

                      if (allActivities.isEmpty) {
                        return const Center(
                          child: Text('Aucune activité trouvée'),
                        );
                      }

                      return ListView.builder(
                        controller: scrollController,
                        itemCount: allActivities.length,
                        itemBuilder: (context, index) {
                          final activity = allActivities[index];
                          return _buildActivityItem(
                            context,
                            activity,
                            appProvider,
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  List<ActivityItem> _getAllActivities(
    BuildContext context,
    AppProvider appProvider,
  ) {
    final activities = <ActivityItem>[];

    // Add all grades from last 30 days
    final recentGrades = appProvider.grades
        .where(
          (grade) => grade.createdAt.isAfter(
            DateTime.now().subtract(const Duration(days: 30)),
          ),
        )
        .toList();

    for (final grade in recentGrades) {
      final student = appProvider.getStudent(grade.studentId);
      if (student != null) {
        activities.add(
          ActivityItem(
            type: ActivityType.grade,
            title: 'Note ajoutée: ${grade.title}',
            subtitle: '${student.fullName} - ${grade.displayValue}',
            timestamp: grade.createdAt,
            icon: Icons.grade,
            color: AppTheme.getGradeColor(grade.normalizedValue),
            onTap: () => Navigator.pop(context),
          ),
        );
      }
    }

    // Add all behavior notes from last 30 days
    final recentBehaviorNotes = appProvider.behaviorNotes
        .where(
          (note) => note.createdAt.isAfter(
            DateTime.now().subtract(const Duration(days: 30)),
          ),
        )
        .toList();

    for (final note in recentBehaviorNotes) {
      final student = appProvider.getStudent(note.studentId);
      if (student != null) {
        activities.add(
          ActivityItem(
            type: ActivityType.behavior,
            title: 'Note de comportement',
            subtitle: '${student.fullName} - ${note.typeDisplayName}',
            timestamp: note.createdAt,
            icon: _getBehaviorIcon(note.type),
            color: _getBehaviorColor(note.type),
            onTap: () => Navigator.pop(context),
          ),
        );
      }
    }

    // Add all students from last 30 days
    final recentStudents = appProvider.students
        .where(
          (student) => student.createdAt.isAfter(
            DateTime.now().subtract(const Duration(days: 30)),
          ),
        )
        .toList();

    for (final student in recentStudents) {
      activities.add(
        ActivityItem(
          type: ActivityType.student,
          title: 'Nouvel élève ajouté',
          subtitle: student.fullName,
          timestamp: student.createdAt,
          icon: Icons.person_add,
          color: AppTheme.primaryGreen,
          onTap: () => Navigator.pop(context),
        ),
      );
    }

    // Sort by timestamp (most recent first)
    activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return activities;
  }
}

enum ActivityType { grade, behavior, student, attendance, lesson }

class ActivityItem {
  final ActivityType type;
  final String title;
  final String subtitle;
  final DateTime timestamp;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  ActivityItem({
    required this.type,
    required this.title,
    required this.subtitle,
    required this.timestamp,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}
