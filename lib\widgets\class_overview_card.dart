import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/behavior_note.dart';
import '../utils/app_theme.dart';
import '../utils/app_router.dart';

class ClassOverviewCard extends StatelessWidget {
  const ClassOverviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final students = appProvider.activeStudents;
        final recentGrades = appProvider.grades
            .where(
              (grade) => grade.date.isAfter(
                DateTime.now().subtract(const Duration(days: 7)),
              ),
            )
            .toList();
        final recentBehaviorNotes = appProvider.behaviorNotes
            .where(
              (note) => note.date.isAfter(
                DateTime.now().subtract(const Duration(days: 7)),
              ),
            )
            .toList();

        // Calculate class average
        double classAverage = 0.0;
        if (recentGrades.isNotEmpty) {
          classAverage =
              recentGrades.fold(
                0.0,
                (sum, grade) => sum + grade.normalizedValue,
              ) /
              recentGrades.length;
        }

        // Calculate behavior statistics
        final positiveNotes = recentBehaviorNotes
            .where((n) => n.type == BehaviorType.positive)
            .length;
        final negativeNotes = recentBehaviorNotes
            .where((n) => n.type == BehaviorType.negative)
            .length;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Vue d\'ensemble de la classe',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to class analytics (to be implemented)
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Analyses de classe - À implémenter'),
                      ),
                    );
                  },
                  child: const Text('Voir détails'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    // Class size and average
                    Row(
                      children: [
                        Expanded(
                          child: _buildOverviewItem(
                            icon: Icons.people,
                            title: 'Élèves actifs',
                            value: students.length.toString(),
                            color: AppTheme.primaryBlue,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildOverviewItem(
                            icon: Icons.trending_up,
                            title: 'Moyenne classe',
                            value: recentGrades.isNotEmpty
                                ? '${classAverage.toStringAsFixed(1)}/20'
                                : 'N/A',
                            color: AppTheme.getGradeColor(classAverage),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Behavior overview
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Comportement cette semaine',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: _buildBehaviorStat(
                                  icon: Icons.thumb_up,
                                  label: 'Positif',
                                  count: positiveNotes,
                                  color: AppTheme.successGreen,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildBehaviorStat(
                                  icon: Icons.thumb_down,
                                  label: 'Négatif',
                                  count: negativeNotes,
                                  color: AppTheme.accentRed,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Quick actions
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => Navigator.pushNamed(
                              context,
                              AppRouter.attendance,
                            ),
                            icon: const Icon(Icons.check_circle, size: 20),
                            label: const Text('Faire l\'appel'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.successGreen,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => Navigator.pushNamed(
                              context,
                              AppRouter.addGrade,
                              arguments: <String, dynamic>{},
                            ),
                            icon: const Icon(Icons.grade, size: 20),
                            label: const Text('Ajouter note'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildOverviewItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBehaviorStat({
    required IconData icon,
    required String label,
    required int count,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
