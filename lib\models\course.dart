import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'course.g.dart';

// French Education System Levels
@HiveType(typeId: 20)
enum EducationLevel {
  @HiveField(0)
  cp, // Cours Préparatoire
  @HiveField(1)
  ce1, // Cours Élémentaire 1
  @HiveField(2)
  ce2, // Cours Élémentaire 2
  @HiveField(3)
  cm1, // Cours Moyen 1
  @HiveField(4)
  cm2, // Cours Moyen 2
  @HiveField(5)
  sixieme, // 6ème
  @HiveField(6)
  cinquieme, // 5ème
  @HiveField(7)
  quatrieme, // 4ème
  @HiveField(8)
  troisieme, // 3ème
  @HiveField(9)
  seconde, // 2nde
  @HiveField(10)
  premiere, // 1ère
  @HiveField(11)
  terminale, // Terminale
}

// Course Categories aligned with French curriculum
@HiveType(typeId: 21)
enum CourseCategory {
  @HiveField(0)
  languesFrancaises, // Français
  @HiveField(1)
  mathematiques, // Mathématiques
  @HiveField(2)
  sciences, // Sciences (SVT, Physique-Chimie)
  @HiveField(3)
  histoireGeographie, // Histoire-Géographie
  @HiveField(4)
  languesVivantes, // Langues vivantes (Anglais, Espagnol, etc.)
  @HiveField(5)
  arts, // Arts plastiques, Éducation musicale
  @HiveField(6)
  eps, // Éducation Physique et Sportive
  @HiveField(7)
  technologie, // Technologie
  @HiveField(8)
  educationCivique, // Éducation civique et morale
  @HiveField(9)
  philosophie, // Philosophie (Terminale)
  @HiveField(10)
  specialites, // Spécialités (Lycée)
  @HiveField(11)
  autre, // Autres matières
}

// Course difficulty level
@HiveType(typeId: 22)
enum CourseDifficulty {
  @HiveField(0)
  debutant, // Beginner
  @HiveField(1)
  intermediaire, // Intermediate
  @HiveField(2)
  avance, // Advanced
  @HiveField(3)
  expert, // Expert
}

// Subject model - represents a specific subject within a course category
@HiveType(typeId: 23)
class Subject extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name; // e.g., "Français", "Mathématiques"

  @HiveField(2)
  late String code; // e.g., "FR", "MATH"

  @HiveField(3)
  late CourseCategory category;

  @HiveField(4)
  String? description;

  @HiveField(5)
  String? color; // Hex color for UI display

  @HiveField(6)
  bool isActive;

  @HiveField(7)
  late DateTime createdAt;

  @HiveField(8)
  late DateTime updatedAt;

  Subject({
    String? id,
    required this.name,
    required this.code,
    required this.category,
    this.description,
    this.color,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'category': category.name,
      'description': description,
      'color': color,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Create from JSON
  factory Subject.fromJson(Map<String, dynamic> json) {
    return Subject(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      category: CourseCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => CourseCategory.autre,
      ),
      description: json['description'],
      color: json['color'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Subject copyWith({
    String? name,
    String? code,
    CourseCategory? category,
    String? description,
    String? color,
    bool? isActive,
  }) {
    return Subject(
      id: id,
      name: name ?? this.name,
      code: code ?? this.code,
      category: category ?? this.category,
      description: description ?? this.description,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }
}

// Learning objective for courses
@HiveType(typeId: 24)
class LearningObjective extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String title;

  @HiveField(2)
  String? description;

  @HiveField(3)
  bool isCompleted;

  @HiveField(4)
  int order; // Display order

  LearningObjective({
    String? id,
    required this.title,
    this.description,
    this.isCompleted = false,
    this.order = 0,
  }) {
    this.id = id ?? const Uuid().v4();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'isCompleted': isCompleted,
      'order': order,
    };
  }

  factory LearningObjective.fromJson(Map<String, dynamic> json) {
    return LearningObjective(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      isCompleted: json['isCompleted'] ?? false,
      order: json['order'] ?? 0,
    );
  }
}

// Course model - represents a specific course/curriculum
@HiveType(typeId: 25)
class Course extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name; // e.g., "Français CP", "Mathématiques 6ème"

  @HiveField(2)
  late String code; // e.g., "FR-CP-2024", "MATH-6-2024"

  @HiveField(3)
  late String subjectId; // Reference to Subject

  @HiveField(4)
  late EducationLevel level;

  @HiveField(5)
  String? description;

  @HiveField(6)
  List<LearningObjective>? objectives;

  @HiveField(7)
  List<String>? prerequisites; // Course IDs that should be completed first

  @HiveField(8)
  CourseDifficulty difficulty;

  @HiveField(9)
  int estimatedHours; // Estimated course duration in hours

  @HiveField(10)
  String? academicYear; // e.g., "2024-2025"

  @HiveField(11)
  bool isActive;

  @HiveField(12)
  late DateTime createdAt;

  @HiveField(13)
  late DateTime updatedAt;

  @HiveField(14)
  String? notes; // Teacher's notes about the course

  Course({
    String? id,
    required this.name,
    required this.code,
    required this.subjectId,
    required this.level,
    this.description,
    this.objectives,
    this.prerequisites,
    this.difficulty = CourseDifficulty.intermediaire,
    this.estimatedHours = 0,
    this.academicYear,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.notes,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    objectives ??= [];
    prerequisites ??= [];
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'subjectId': subjectId,
      'level': level.name,
      'description': description,
      'objectives': objectives?.map((obj) => obj.toJson()).toList(),
      'prerequisites': prerequisites,
      'difficulty': difficulty.name,
      'estimatedHours': estimatedHours,
      'academicYear': academicYear,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'notes': notes,
    };
  }

  // Create from JSON
  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      subjectId: json['subjectId'],
      level: EducationLevel.values.firstWhere(
        (e) => e.name == json['level'],
        orElse: () => EducationLevel.cp,
      ),
      description: json['description'],
      objectives: json['objectives'] != null
          ? (json['objectives'] as List)
              .map((obj) => LearningObjective.fromJson(obj))
              .toList()
          : null,
      prerequisites: json['prerequisites'] != null
          ? List<String>.from(json['prerequisites'])
          : null,
      difficulty: CourseDifficulty.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => CourseDifficulty.intermediaire,
      ),
      estimatedHours: json['estimatedHours'] ?? 0,
      academicYear: json['academicYear'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      notes: json['notes'],
    );
  }

  Course copyWith({
    String? name,
    String? code,
    String? subjectId,
    EducationLevel? level,
    String? description,
    List<LearningObjective>? objectives,
    List<String>? prerequisites,
    CourseDifficulty? difficulty,
    int? estimatedHours,
    String? academicYear,
    bool? isActive,
    String? notes,
  }) {
    return Course(
      id: id,
      name: name ?? this.name,
      code: code ?? this.code,
      subjectId: subjectId ?? this.subjectId,
      level: level ?? this.level,
      description: description ?? this.description,
      objectives: objectives ?? this.objectives,
      prerequisites: prerequisites ?? this.prerequisites,
      difficulty: difficulty ?? this.difficulty,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      academicYear: academicYear ?? this.academicYear,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      notes: notes ?? this.notes,
    );
  }

  // Helper methods
  String get displayName => '$name (${level.name.toUpperCase()})';
  
  double get completionPercentage {
    if (objectives == null || objectives!.isEmpty) return 0.0;
    final completed = objectives!.where((obj) => obj.isCompleted).length;
    return (completed / objectives!.length) * 100;
  }
}
