import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../providers/app_provider.dart';
import '../../services/license_service.dart';
import '../../services/settings_service.dart';

import '../../utils/app_router.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  PackageInfo? _packageInfo;
  final _teacherNameController = TextEditingController();
  final _schoolNameController = TextEditingController();
  final _academicYearController = TextEditingController();
  final _defaultSubjectController = TextEditingController();
  final _defaultTitleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
    _loadCurrentSettings();
  }

  Future<void> _loadPackageInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _packageInfo = packageInfo;
    });
  }

  void _loadCurrentSettings() {
    final settings = SettingsService.currentSettings;
    _teacherNameController.text = settings.teacherName;
    _schoolNameController.text = settings.schoolName;
    _academicYearController.text = settings.academicYear;
    _defaultSubjectController.text = settings.defaultSubject ?? '';
    _defaultTitleController.text = settings.defaultGradeTitle ?? '';
  }

  @override
  void dispose() {
    _teacherNameController.dispose();
    _schoolNameController.dispose();
    _academicYearController.dispose();
    _defaultSubjectController.dispose();
    _defaultTitleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Paramètres'), elevation: 0),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildLicenseSection(),
              const SizedBox(height: 24),
              _buildPersonalInfoSection(),
              const SizedBox(height: 24),
              _buildGradePresetsSection(),
              const SizedBox(height: 24),
              _buildAppearanceSection(appProvider),
              const SizedBox(height: 24),
              _buildDataSection(appProvider),
              const SizedBox(height: 24),
              _buildAboutSection(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLicenseSection() {
    final isLicensed = LicenseService.isLicensed;
    final statusText = LicenseService.licenseStatusText;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isLicensed ? Icons.verified : Icons.lock,
                  color: isLicensed ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Licence',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isLicensed ? Colors.green[50] : Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isLicensed ? Colors.green[200]! : Colors.orange[200]!,
                ),
              ),
              child: Text(
                statusText,
                style: TextStyle(
                  color: isLicensed ? Colors.green[800] : Colors.orange[800],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (!isLicensed) ...[
              const SizedBox(height: 12),
              const Text(
                'Version gratuite - Fonctionnalités limitées:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              const Text('• Maximum 10 élèves'),
              const Text('• Maximum 5 cours'),
              const Text('• Pas d\'export de données'),
              const Text('• Pas de sauvegarde automatique'),
              const Text('• Pas de rapports avancés'),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showLicenseActivationDialog(),
                  icon: const Icon(Icons.coffee),
                  label: const Text('Acheter un café ☕'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
            if (isLicensed) ...[
              const SizedBox(height: 12),
              const Text(
                'Merci pour votre soutien! 🎉',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.person),
                SizedBox(width: 8),
                Text(
                  'Informations personnelles',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _teacherNameController,
              decoration: const InputDecoration(
                labelText: 'Nom de l\'enseignant',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              onChanged: (value) {
                SettingsService.updateSetting('teacherName', value);
              },
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _schoolNameController,
              decoration: const InputDecoration(
                labelText: 'Nom de l\'école',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.school),
              ),
              onChanged: (value) {
                SettingsService.updateSetting('schoolName', value);
              },
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _academicYearController,
              decoration: const InputDecoration(
                labelText: 'Année scolaire',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.calendar_today),
                hintText: '2024-2025',
              ),
              onChanged: (value) {
                SettingsService.updateSetting('academicYear', value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppearanceSection(AppProvider appProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.palette),
                SizedBox(width: 8),
                Text(
                  'Apparence',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Mode sombre'),
              subtitle: const Text('Utiliser le thème sombre'),
              value: appProvider.settings.isDarkMode,
              onChanged: (value) {
                appProvider.updateSetting('isDarkMode', value);
              },
              secondary: const Icon(Icons.dark_mode),
            ),
            ListTile(
              title: const Text('Taille de police'),
              subtitle: Text('${appProvider.settings.fontSize.toInt()}px'),
              leading: const Icon(Icons.text_fields),
              trailing: SizedBox(
                width: 150,
                child: Slider(
                  value: appProvider.settings.fontSize,
                  min: 12.0,
                  max: 20.0,
                  divisions: 8,
                  onChanged: (value) {
                    appProvider.updateSetting('fontSize', value);
                  },
                ),
              ),
            ),
            SwitchListTile(
              title: const Text('Photos des élèves'),
              subtitle: const Text('Afficher les photos dans les listes'),
              value: appProvider.settings.showStudentPhotos,
              onChanged: (value) {
                appProvider.updateSetting('showStudentPhotos', value);
              },
              secondary: const Icon(Icons.photo),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradePresetsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.grade),
                SizedBox(width: 8),
                Text(
                  'Préréglages des notes',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _defaultSubjectController,
              decoration: const InputDecoration(
                labelText: 'Matière par défaut',
                hintText: 'Ex: Mathématiques',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.subject),
                helperText:
                    'Matière pré-sélectionnée lors de l\'ajout d\'une note',
              ),
              onChanged: (value) {
                SettingsService.updateSetting(
                  'defaultSubject',
                  value.isEmpty ? null : value,
                );
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _defaultTitleController,
              decoration: const InputDecoration(
                labelText: 'Titre par défaut',
                hintText: 'Ex: Contrôle',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
                helperText: 'Titre pré-rempli lors de l\'ajout d\'une note',
              ),
              onChanged: (value) {
                SettingsService.updateSetting(
                  'defaultGradeTitle',
                  value.isEmpty ? null : value,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSection(AppProvider appProvider) {
    final isLicensed = LicenseService.isLicensed;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.storage),
                SizedBox(width: 8),
                Text(
                  'Données',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('Sauvegarde automatique'),
              subtitle: Text(
                isLicensed
                    ? (appProvider.settings.autoBackup
                          ? 'Activée'
                          : 'Désactivée')
                    : 'Fonctionnalité premium',
              ),
              leading: const Icon(Icons.backup),
              trailing: isLicensed
                  ? Switch(
                      value: appProvider.settings.autoBackup,
                      onChanged: (value) {
                        appProvider.updateSetting('autoBackup', value);
                      },
                    )
                  : const Icon(Icons.lock, color: Colors.orange),
              onTap: !isLicensed
                  ? () => LicenseService.showRestrictionDialog(
                      context,
                      'backup_restore',
                    )
                  : null,
            ),
            if (isLicensed && appProvider.settings.autoBackup)
              ListTile(
                title: const Text('Fréquence de sauvegarde'),
                subtitle: Text(
                  'Tous les ${appProvider.settings.backupFrequency} jours',
                ),
                leading: const Icon(Icons.schedule),
                trailing: DropdownButton<int>(
                  value: appProvider.settings.backupFrequency,
                  items: [1, 3, 7, 14, 30].map((days) {
                    return DropdownMenuItem(
                      value: days,
                      child: Text('$days jour${days > 1 ? 's' : ''}'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      appProvider.updateSetting('backupFrequency', value);
                    }
                  },
                ),
              ),
            ListTile(
              title: const Text('Exporter les données'),
              subtitle: Text(
                isLicensed
                    ? 'Exporter vers ${appProvider.settings.exportFormat.toUpperCase()}'
                    : 'Fonctionnalité premium',
              ),
              leading: const Icon(Icons.file_download),
              trailing: isLicensed
                  ? const Icon(Icons.arrow_forward_ios)
                  : const Icon(Icons.lock, color: Colors.orange),
              onTap: () {
                if (isLicensed) {
                  Navigator.pushNamed(context, AppRouter.backup);
                } else {
                  LicenseService.showRestrictionDialog(context, 'export_data');
                }
              },
            ),
            ListTile(
              title: const Text('Réinitialiser les paramètres'),
              subtitle: const Text('Restaurer les paramètres par défaut'),
              leading: const Icon(Icons.restore),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showResetDialog(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info),
                SizedBox(width: 8),
                Text(
                  'À propos',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('Version'),
              subtitle: Text(_packageInfo?.version ?? 'Inconnue'),
              leading: const Icon(Icons.info_outline),
            ),
            ListTile(
              title: const Text('Build'),
              subtitle: Text(_packageInfo?.buildNumber ?? 'Inconnue'),
              leading: const Icon(Icons.build),
            ),
            ListTile(
              title: const Text('Manuel d\'utilisation'),
              subtitle: const Text('Guide complet de l\'application'),
              leading: const Icon(Icons.help_outline),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                Navigator.pushNamed(context, AppRouter.userManual);
              },
            ),
            ListTile(
              title: const Text('Développeur'),
              subtitle: const Text('Contactez-nous'),
              leading: const Icon(Icons.developer_mode),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showDeveloperContact(),
            ),
            ListTile(
              title: const Text('Licence'),
              subtitle: const Text('Conditions d\'utilisation'),
              leading: const Icon(Icons.description),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLicenseDialog(),
            ),
          ],
        ),
      ),
    );
  }

  void _showLicenseActivationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Activer la licence'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.coffee, size: 48, color: Colors.orange),
            const SizedBox(height: 16),
            const Text(
              'Soutenez le développement de cette application en achetant un café au développeur!',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                LicenseService.paymentPhoneNumber,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Après le paiement, vous recevrez un code d\'activation.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showPaymentOptions();
            },
            child: const Text('Contacter'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showActivationCodeDialog();
            },
            child: const Text('J\'ai un code'),
          ),
        ],
      ),
    );
  }

  void _showPaymentOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contacter le développeur'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Choisissez votre méthode de contact préférée:',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                LicenseService.paymentPhoneNumber,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await LicenseService.launchPaymentWhatsApp();
              } catch (e) {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
              }
            },
            child: const Text('WhatsApp'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await LicenseService.launchPaymentSMS();
              } catch (e) {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
              }
            },
            child: const Text('SMS'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await LicenseService.launchPaymentCall();
              } catch (e) {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
              }
            },
            child: const Text('Appeler'),
          ),
        ],
      ),
    );
  }

  void _showActivationCodeDialog() {
    final codeController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Code d\'activation'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Entrez le code d\'activation reçu après votre paiement:',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: codeController,
              decoration: const InputDecoration(
                labelText: 'Code d\'activation',
                border: OutlineInputBorder(),
                hintText: 'XXXX-XXXX-XXXX',
              ),
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                letterSpacing: 2,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              final code = codeController.text.trim();
              if (code.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Veuillez entrer un code')),
                );
                return;
              }

              // Simple validation - in a real app, this would be server-side
              if (code.toUpperCase() == 'CAFE-2024-GESTION' ||
                  code.toUpperCase() == 'PREMIUM-ACCESS-2024') {
                await LicenseService.activateLicense();
                Navigator.of(context).pop();
                setState(() {});
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Licence activée avec succès! 🎉'),
                    backgroundColor: Colors.green,
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Code d\'activation invalide'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Activer'),
          ),
        ],
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Réinitialiser les paramètres'),
        content: const Text(
          'Êtes-vous sûr de vouloir restaurer tous les paramètres par défaut? Cette action ne peut pas être annulée.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              await SettingsService.resetToDefaults();
              Navigator.of(context).pop();
              setState(() {});
              _loadCurrentSettings();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Paramètres réinitialisés')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Réinitialiser'),
          ),
        ],
      ),
    );
  }

  void _showDeveloperContact() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contacter le développeur'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Pour toute question, suggestion ou problème:',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Text(
                    LicenseService.developerEmail,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LicenseService.paymentPhoneNumber,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Disponible du lundi au vendredi, 9h-17h',
              style: TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showPaymentOptions();
            },
            child: const Text('Contacter'),
          ),
        ],
      ),
    );
  }

  void _showLicenseDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Conditions d\'utilisation'),
        content: const SingleChildScrollView(
          child: Text(
            'LICENCE D\'UTILISATION\n\n'
            '1. Cette application est fournie "en l\'état" sans garantie.\n\n'
            '2. L\'utilisation de cette application est soumise aux conditions suivantes:\n'
            '   - Usage personnel et professionnel autorisé\n'
            '   - Interdiction de redistribution\n'
            '   - Respect de la vie privée des élèves\n\n'
            '3. Le développeur ne peut être tenu responsable des dommages résultant de l\'utilisation de cette application.\n\n'
            '4. Les données sont stockées localement sur votre appareil.\n\n'
            '5. Pour toute question, contactez le développeur.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
