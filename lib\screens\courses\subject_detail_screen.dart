import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class SubjectDetailScreen extends StatefulWidget {
  final String subjectId;

  const SubjectDetailScreen({super.key, required this.subjectId});

  @override
  State<SubjectDetailScreen> createState() => _SubjectDetailScreenState();
}

class _SubjectDetailScreenState extends State<SubjectDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Subject? _subject;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSubjectData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadSubjectData() {
    final appProvider = context.read<AppProvider>();
    _subject = appProvider.getSubject(widget.subjectId);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_subject == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Matière introuvable')),
        body: const Center(
          child: Text('Cette matière n\'existe pas ou a été supprimée.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_subject!.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.pushNamed(
                context,
                AppRouter.editSubject,
                arguments: widget.subjectId,
              );
            },
            tooltip: 'Modifier la matière',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeleteConfirmation();
              } else if (value == 'addCourse') {
                Navigator.pushNamed(
                  context,
                  AppRouter.addCourse,
                  arguments: {'subjectId': widget.subjectId},
                );
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'addCourse',
                child: Row(
                  children: [
                    Icon(Icons.add),
                    SizedBox(width: 8),
                    Text('Ajouter un cours'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Supprimer', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Aperçu', icon: Icon(Icons.info_outline)),
            Tab(text: 'Cours', icon: Icon(Icons.school_outlined)),
            Tab(text: 'Statistiques', icon: Icon(Icons.analytics_outlined)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildCoursesTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Subject header card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: _getSubjectColor().withValues(
                          alpha: 0.1,
                        ),
                        child: Icon(
                          _getCategoryIcon(_subject!.category),
                          color: _getSubjectColor(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _subject!.name,
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            Text(
                              _subject!.code,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Chip(
                        label: Text(_getCategoryName(_subject!.category)),
                        backgroundColor: _getSubjectColor().withValues(
                          alpha: 0.1,
                        ),
                      ),
                    ],
                  ),
                  if (_subject!.description != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Description',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_subject!.description!),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Subject details
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Détails de la matière',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow('Code', _subject!.code),
                  _buildDetailRow(
                    'Catégorie',
                    _getCategoryName(_subject!.category),
                  ),
                  _buildDetailRow(
                    'Couleur',
                    '',
                    trailing: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: _getSubjectColor(),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                    ),
                  ),
                  _buildDetailRow(
                    'Statut',
                    _subject!.isActive ? 'Active' : 'Inactive',
                  ),
                  _buildDetailRow('Créée le', _formatDate(_subject!.createdAt)),
                  _buildDetailRow(
                    'Modifiée le',
                    _formatDate(_subject!.updatedAt),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoursesTab() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final courses = appProvider.getCoursesBySubject(widget.subjectId);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      'Cours de ${_subject!.name} (${courses.length})',
                      style: Theme.of(context).textTheme.headlineSmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        AppRouter.addCourse,
                        arguments: {'subjectId': widget.subjectId},
                      );
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Nouveau cours'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              if (courses.isEmpty)
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text('Aucun cours créé pour cette matière.'),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: courses.length,
                  itemBuilder: (context, index) {
                    final course = courses[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: _getSubjectColor().withValues(
                            alpha: 0.1,
                          ),
                          child: Icon(Icons.school, color: _getSubjectColor()),
                        ),
                        title: Text(course.name),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(course.code),
                            Text(
                              '${_getLevelName(course.level)} • ${_getDifficultyName(course.difficulty)}',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (!course.isActive)
                              const Icon(
                                Icons.visibility_off,
                                color: Colors.grey,
                              ),
                            const Icon(Icons.chevron_right),
                          ],
                        ),
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            AppRouter.courseDetail,
                            arguments: course.id,
                          );
                        },
                      ),
                    );
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatisticsTab() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final courses = appProvider.getCoursesBySubject(widget.subjectId);
        final activeCourses = courses.where((c) => c.isActive).length;
        final totalHours = courses.fold(0, (sum, c) => sum + c.estimatedHours);

        // Get grades for this subject
        final allGrades = appProvider.grades
            .where((grade) => grade.subject == _subject!.name)
            .toList();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Statistiques de ${_subject!.name}',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),

              // Course statistics
              Text('Cours', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 8),
              _buildStatCard('Total des cours', courses.length.toString()),
              _buildStatCard('Cours actifs', activeCourses.toString()),
              _buildStatCard(
                'Cours inactifs',
                (courses.length - activeCourses).toString(),
              ),
              if (totalHours > 0)
                _buildStatCard('Heures totales estimées', '${totalHours}h'),

              const SizedBox(height: 24),

              // Grade statistics
              Text(
                'Notes et évaluations',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              _buildStatCard('Notes attribuées', allGrades.length.toString()),
              if (allGrades.isNotEmpty) ...[
                _buildStatCard(
                  'Moyenne générale',
                  '${(allGrades.fold(0.0, (sum, grade) => sum + grade.normalizedValue) / allGrades.length).toStringAsFixed(1)}/20',
                ),
                _buildStatCard(
                  'Note la plus haute',
                  '${allGrades.map((g) => g.normalizedValue).reduce((a, b) => a > b ? a : b).toStringAsFixed(1)}/20',
                ),
                _buildStatCard(
                  'Note la plus basse',
                  '${allGrades.map((g) => g.normalizedValue).reduce((a, b) => a < b ? a : b).toStringAsFixed(1)}/20',
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value, {Widget? trailing}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          if (trailing != null) trailing else Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: _getSubjectColor(),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSubjectColor() {
    if (_subject?.color != null) {
      return Color(int.parse('0xFF${_subject!.color!.substring(1)}'));
    }
    return AppTheme.primaryBlue;
  }

  IconData _getCategoryIcon(CourseCategory category) {
    switch (category) {
      case CourseCategory.mathematiques:
        return Icons.calculate;
      case CourseCategory.sciences:
        return Icons.science;
      case CourseCategory.languesVivantes:
      case CourseCategory.languesFrancaises:
        return Icons.language;
      case CourseCategory.histoireGeographie:
      case CourseCategory.educationCivique:
      case CourseCategory.philosophie:
        return Icons.history_edu;
      case CourseCategory.arts:
        return Icons.palette;
      case CourseCategory.eps:
        return Icons.sports;
      case CourseCategory.technologie:
        return Icons.computer;
      case CourseCategory.specialites:
        return Icons.school;
      case CourseCategory.autre:
        return Icons.subject;
    }
  }

  String _getCategoryName(CourseCategory category) {
    switch (category) {
      case CourseCategory.languesFrancaises:
        return 'Français';
      case CourseCategory.mathematiques:
        return 'Mathématiques';
      case CourseCategory.sciences:
        return 'Sciences';
      case CourseCategory.histoireGeographie:
        return 'Histoire-Géographie';
      case CourseCategory.languesVivantes:
        return 'Langues vivantes';
      case CourseCategory.arts:
        return 'Arts';
      case CourseCategory.eps:
        return 'EPS';
      case CourseCategory.technologie:
        return 'Technologie';
      case CourseCategory.educationCivique:
        return 'Éducation civique';
      case CourseCategory.philosophie:
        return 'Philosophie';
      case CourseCategory.specialites:
        return 'Spécialités';
      case CourseCategory.autre:
        return 'Autre';
    }
  }

  String _getLevelName(EducationLevel level) {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  String _getDifficultyName(CourseDifficulty difficulty) {
    switch (difficulty) {
      case CourseDifficulty.debutant:
        return 'Débutant';
      case CourseDifficulty.intermediaire:
        return 'Intermédiaire';
      case CourseDifficulty.avance:
        return 'Avancé';
      case CourseDifficulty.expert:
        return 'Expert';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la matière'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer la matière "${_subject!.name}" ? Cette action supprimera également tous les cours associés. Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSubject();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _deleteSubject() async {
    try {
      await context.read<AppProvider>().deleteSubject(widget.subjectId);
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Matière supprimée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la suppression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
