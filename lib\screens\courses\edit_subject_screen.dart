import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';
import '../../utils/app_theme.dart';

class EditSubjectScreen extends StatefulWidget {
  final String subjectId;

  const EditSubjectScreen({super.key, required this.subjectId});

  @override
  State<EditSubjectScreen> createState() => _EditSubjectScreenState();
}

class _EditSubjectScreenState extends State<EditSubjectScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _descriptionController = TextEditingController();

  CourseCategory _selectedCategory = CourseCategory.autre;
  Color _selectedColor = AppTheme.primaryBlue;
  bool _isActive = true;
  Subject? _subject;

  final List<Color> _availableColors = [
    AppTheme.primaryBlue,
    AppTheme.accentRed,
    AppTheme.successGreen,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.amber,
    Colors.cyan,
    Colors.lime,
    Colors.deepOrange,
  ];

  @override
  void initState() {
    super.initState();
    _loadSubject();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _loadSubject() {
    final appProvider = context.read<AppProvider>();
    _subject = appProvider.getSubject(widget.subjectId);

    if (_subject != null) {
      _nameController.text = _subject!.name;
      _codeController.text = _subject!.code;
      _descriptionController.text = _subject!.description ?? '';
      _selectedCategory = _subject!.category;
      _isActive = _subject!.isActive;

      // Parse color
      if (_subject!.color != null) {
        try {
          _selectedColor = Color(
            int.parse('0xFF${_subject!.color!.substring(1)}'),
          );
        } catch (e) {
          _selectedColor = AppTheme.primaryBlue;
        }
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_subject == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Matière introuvable')),
        body: const Center(
          child: Text('Cette matière n\'existe pas ou a été supprimée.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Modifier la matière'),
        actions: [
          TextButton(onPressed: _saveSubject, child: const Text('Enregistrer')),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildCategorySection(),
              const SizedBox(height: 24),
              _buildColorSection(),
              const SizedBox(height: 24),
              _buildDescriptionSection(),
              const SizedBox(height: 24),
              _buildStatusSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de base',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la matière *',
                isDense: true,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Veuillez saisir un nom de matière';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Code de la matière *',
                isDense: true,
                hintText: 'Ex: MATH, FR, HIST',
              ),
              textCapitalization: TextCapitalization.characters,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Veuillez saisir un code de matière';
                }
                if (value.trim().length > 10) {
                  return 'Le code ne peut pas dépasser 10 caractères';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Catégorie', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            DropdownButtonFormField<CourseCategory>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Catégorie de la matière',
                isDense: true,
              ),
              items: CourseCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(_getCategoryIcon(category), size: 20),
                      const SizedBox(width: 8),
                      Text(_getCategoryName(category)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Couleur de la matière',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: _availableColors.map((color) {
                final isSelected = color.value == _selectedColor.value;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedColor = color;
                    });
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? Colors.black : Colors.grey[300]!,
                        width: isSelected ? 3 : 1,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 24)
                        : null,
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: _selectedColor,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Couleur sélectionnée',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Description', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description de la matière',
                isDense: true,
                hintText: 'Description optionnelle de la matière...',
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statut de la matière',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Matière active'),
              subtitle: const Text(
                'La matière est disponible pour créer des cours',
              ),
              value: _isActive,
              onChanged: (value) {
                setState(() {
                  _isActive = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(CourseCategory category) {
    switch (category) {
      case CourseCategory.mathematiques:
        return Icons.calculate;
      case CourseCategory.sciences:
        return Icons.science;
      case CourseCategory.languesVivantes:
      case CourseCategory.languesFrancaises:
        return Icons.language;
      case CourseCategory.histoireGeographie:
      case CourseCategory.educationCivique:
      case CourseCategory.philosophie:
        return Icons.history_edu;
      case CourseCategory.arts:
        return Icons.palette;
      case CourseCategory.eps:
        return Icons.sports;
      case CourseCategory.technologie:
        return Icons.computer;
      case CourseCategory.specialites:
        return Icons.school;
      case CourseCategory.autre:
        return Icons.subject;
    }
  }

  String _getCategoryName(CourseCategory category) {
    switch (category) {
      case CourseCategory.languesFrancaises:
        return 'Français';
      case CourseCategory.mathematiques:
        return 'Mathématiques';
      case CourseCategory.sciences:
        return 'Sciences';
      case CourseCategory.histoireGeographie:
        return 'Histoire-Géographie';
      case CourseCategory.languesVivantes:
        return 'Langues vivantes';
      case CourseCategory.arts:
        return 'Arts';
      case CourseCategory.eps:
        return 'EPS';
      case CourseCategory.technologie:
        return 'Technologie';
      case CourseCategory.educationCivique:
        return 'Éducation civique';
      case CourseCategory.philosophie:
        return 'Philosophie';
      case CourseCategory.specialites:
        return 'Spécialités';
      case CourseCategory.autre:
        return 'Autre';
    }
  }

  void _saveSubject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check if code is unique (excluding current subject)
    final appProvider = context.read<AppProvider>();
    final existingSubject = appProvider.activeSubjects.firstWhere(
      (s) =>
          s.code.toUpperCase() == _codeController.text.trim().toUpperCase() &&
          s.id != widget.subjectId,
      orElse: () => Subject(name: '', code: '', category: CourseCategory.autre),
    );

    if (existingSubject.name.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ce code de matière existe déjà'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final updatedSubject = _subject!.copyWith(
        name: _nameController.text.trim(),
        code: _codeController.text.trim().toUpperCase(),
        category: _selectedCategory,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        color:
            '#${_selectedColor.toARGB32().toRadixString(16).substring(2).toUpperCase()}',
        isActive: _isActive,
      );

      await appProvider.updateSubject(updatedSubject);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Matière modifiée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
