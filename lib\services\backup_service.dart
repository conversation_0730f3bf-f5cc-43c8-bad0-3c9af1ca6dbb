import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import 'database_service.dart';
import 'settings_service.dart';

class BackupService {
  static const String backupVersion = '1.0';
  static const String backupFileExtension = '.isoucklou_backup';

  /// Create a complete backup of all app data
  static Future<Map<String, dynamic>> createBackup() async {
    final timestamp = DateTime.now();
    
    // Get all data from database
    final databaseData = await DatabaseService.exportData();
    
    // Get app settings
    final settings = SettingsService.currentSettings;
    
    // Create backup structure
    final backup = {
      'version': backupVersion,
      'timestamp': timestamp.toIso8601String(),
      'appName': 'isoucklou',
      'data': {
        'settings': settings.toJson(),
        'database': databaseData,
      },
      'metadata': {
        'totalStudents': DatabaseService.allStudents.length,
        'totalGrades': DatabaseService.gradesBox.length,
        'totalBehaviorNotes': DatabaseService.behaviorNotesBox.length,
        'totalLessons': DatabaseService.lessonsBox.length,
        'totalCourses': DatabaseService.allCourses.length,
        'totalSubjects': DatabaseService.allSubjects.length,
      }
    };
    
    return backup;
  }

  /// Export backup to file
  static Future<String?> exportBackupToFile() async {
    try {
      // Create backup data
      final backup = await createBackup();
      
      // Convert to JSON string
      final jsonString = jsonEncode(backup);
      
      // Get documents directory
      final directory = await getApplicationDocumentsDirectory();
      
      // Create filename with timestamp
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final filename = 'isoucklou_backup_$timestamp$backupFileExtension';
      
      // Create file
      final file = File('${directory.path}/$filename');
      await file.writeAsString(jsonString);
      
      return file.path;
    } catch (e) {
      debugPrint('Error exporting backup: $e');
      return null;
    }
  }

  /// Share backup file
  static Future<bool> shareBackup() async {
    try {
      final filePath = await exportBackupToFile();
      if (filePath != null) {
        await Share.shareXFiles(
          [XFile(filePath)],
          text: 'Sauvegarde isoucklou - ${DateFormat('dd/MM/yyyy').format(DateTime.now())}',
        );
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error sharing backup: $e');
      return false;
    }
  }

  /// Pick and restore backup file
  static Future<bool> pickAndRestoreBackup() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['isoucklou_backup', 'json'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        return await restoreFromFile(file);
      }
      return false;
    } catch (e) {
      debugPrint('Error picking backup file: $e');
      return false;
    }
  }

  /// Restore from backup file
  static Future<bool> restoreFromFile(File file) async {
    try {
      // Read file content
      final jsonString = await file.readAsString();
      final backup = jsonDecode(jsonString) as Map<String, dynamic>;
      
      // Validate backup
      if (!_validateBackup(backup)) {
        throw Exception('Fichier de sauvegarde invalide');
      }
      
      // Restore data
      await _restoreData(backup);
      
      return true;
    } catch (e) {
      debugPrint('Error restoring backup: $e');
      return false;
    }
  }

  /// Validate backup structure
  static bool _validateBackup(Map<String, dynamic> backup) {
    try {
      // Check required fields
      if (!backup.containsKey('version') ||
          !backup.containsKey('timestamp') ||
          !backup.containsKey('data')) {
        return false;
      }

      // Check data structure
      final data = backup['data'] as Map<String, dynamic>;
      if (!data.containsKey('settings') || !data.containsKey('database')) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Restore data from backup
  static Future<void> _restoreData(Map<String, dynamic> backup) async {
    final data = backup['data'] as Map<String, dynamic>;
    
    // Restore database data
    if (data['database'] != null) {
      await DatabaseService.importData(data['database'] as Map<String, dynamic>);
    }
    
    // Restore settings
    if (data['settings'] != null) {
      final settingsData = data['settings'] as Map<String, dynamic>;
      await _restoreSettings(settingsData);
    }
  }

  /// Restore app settings
  static Future<void> _restoreSettings(Map<String, dynamic> settingsData) async {
    // Update each setting individually to ensure proper validation
    for (final entry in settingsData.entries) {
      try {
        await SettingsService.updateSetting(entry.key, entry.value);
      } catch (e) {
        debugPrint('Error restoring setting ${entry.key}: $e');
      }
    }
  }

  /// Get backup file info
  static Future<Map<String, dynamic>?> getBackupInfo(File file) async {
    try {
      final jsonString = await file.readAsString();
      final backup = jsonDecode(jsonString) as Map<String, dynamic>;
      
      if (!_validateBackup(backup)) {
        return null;
      }
      
      return {
        'version': backup['version'],
        'timestamp': DateTime.parse(backup['timestamp']),
        'metadata': backup['metadata'] ?? {},
      };
    } catch (e) {
      return null;
    }
  }

  /// Export specific data type
  static Future<String?> exportSpecificData(String dataType) async {
    try {
      Map<String, dynamic> data = {};
      String filename = '';
      
      switch (dataType) {
        case 'students':
          data = {
            'students': DatabaseService.studentsBox.values
                .map((s) => s.toJson())
                .toList(),
          };
          filename = 'students_export';
          break;
        case 'grades':
          data = {
            'grades': DatabaseService.gradesBox.values
                .map((g) => g.toJson())
                .toList(),
          };
          filename = 'grades_export';
          break;
        case 'attendance':
          data = {
            'attendance': DatabaseService.attendanceBox.values
                .map((a) => a.toJson())
                .toList(),
            'dailyAttendance': DatabaseService.dailyAttendanceBox.values
                .map((da) => da.toJson())
                .toList(),
          };
          filename = 'attendance_export';
          break;
        case 'behavior':
          data = {
            'behaviorNotes': DatabaseService.behaviorNotesBox.values
                .map((bn) => bn.toJson())
                .toList(),
          };
          filename = 'behavior_export';
          break;
        default:
          return null;
      }
      
      // Add metadata
      data['exportedAt'] = DateTime.now().toIso8601String();
      data['exportType'] = dataType;
      
      // Convert to JSON and save
      final jsonString = jsonEncode(data);
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final file = File('${directory.path}/${filename}_$timestamp.json');
      await file.writeAsString(jsonString);
      
      return file.path;
    } catch (e) {
      debugPrint('Error exporting $dataType: $e');
      return null;
    }
  }

  /// Auto backup if enabled
  static Future<void> performAutoBackup() async {
    final settings = SettingsService.currentSettings;
    
    if (!settings.autoBackup) {
      return;
    }
    
    try {
      // Check if backup is needed based on frequency
      final lastBackup = await _getLastBackupDate();
      final now = DateTime.now();
      
      if (lastBackup == null || 
          now.difference(lastBackup).inDays >= settings.backupFrequency) {
        
        // Perform backup
        await exportBackupToFile();
        await _setLastBackupDate(now);
        
        debugPrint('Auto backup completed');
      }
    } catch (e) {
      debugPrint('Auto backup failed: $e');
    }
  }

  /// Get last backup date
  static Future<DateTime?> _getLastBackupDate() async {
    try {
      final settings = SettingsService.currentSettings;
      // This would be stored in settings or preferences
      // For now, return null to always perform backup
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Set last backup date
  static Future<void> _setLastBackupDate(DateTime date) async {
    try {
      // This would store the date in settings or preferences
      // Implementation depends on how you want to store this
      debugPrint('Last backup date set to: $date');
    } catch (e) {
      debugPrint('Error setting last backup date: $e');
    }
  }
}
