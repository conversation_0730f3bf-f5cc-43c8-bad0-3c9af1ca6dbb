import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import '../models/grade.dart';
import '../models/student.dart';
import '../utils/app_theme.dart';

class GradeCard extends StatelessWidget {
  final Grade grade;
  final Student? student;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showStudent;

  const GradeCard({
    super.key,
    required this.grade,
    this.student,
    this.onEdit,
    this.onDelete,
    this.showStudent = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 12),
            _buildGradeInfo(),
            if (grade.description != null || grade.feedback != null) ...[
              const SizedBox(height: 12),
              _buildAdditionalInfo(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        if (showStudent && student != null) ...[
          _buildStudentAvatar(),
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                grade.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  if (showStudent && student != null) ...[
                    Text(
                      student!.fullName,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Text(' • '),
                  ],
                  Text(
                    DateFormat('dd/MM/yyyy').format(grade.date),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        _buildGradeDisplay(),
        _buildActionMenu(),
      ],
    );
  }

  Widget _buildStudentAvatar() {
    return CircleAvatar(
      radius: 20,
      backgroundColor: AppTheme.primaryBlue.withValues(alpha: 0.1),
      backgroundImage: student?.photoPath != null && File(student!.photoPath!).existsSync()
          ? FileImage(File(student!.photoPath!))
          : null,
      child: student?.photoPath == null || !File(student!.photoPath!).existsSync()
          ? Text(
              student?.initials ?? '?',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryBlue,
              ),
            )
          : null,
    );
  }

  Widget _buildGradeDisplay() {
    final color = AppTheme.getGradeColor(grade.normalizedValue);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color, width: 2),
      ),
      child: Column(
        children: [
          Text(
            grade.displayValue,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            grade.appreciation,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionMenu() {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 20),
              SizedBox(width: 8),
              Text('Modifier'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 20, color: Colors.red),
              SizedBox(width: 8),
              Text('Supprimer', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
      child: const Icon(Icons.more_vert, size: 20),
    );
  }

  Widget _buildGradeInfo() {
    return Row(
      children: [
        _buildInfoChip(
          grade.typeDisplayName,
          _getTypeColor(grade.type),
          _getTypeIcon(grade.type),
        ),
        const SizedBox(width: 8),
        if (grade.coefficient != 1.0)
          _buildInfoChip(
            'Coeff. ${grade.coefficient}',
            AppTheme.neutralGray,
            Icons.functions,
          ),
        const Spacer(),
        if (grade.isPublished)
          _buildInfoChip(
            'Publié',
            AppTheme.successGreen,
            Icons.visibility,
          )
        else
          _buildInfoChip(
            'Brouillon',
            AppTheme.warningOrange,
            Icons.visibility_off,
          ),
      ],
    );
  }

  Widget _buildInfoChip(String label, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (grade.description != null && grade.description!.isNotEmpty) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Description',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  grade.description!,
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
        if (grade.feedback != null && grade.feedback!.isNotEmpty) ...[
          if (grade.description != null && grade.description!.isNotEmpty)
            const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryBlue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.primaryBlue.withValues(alpha: 0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.feedback,
                      size: 16,
                      color: AppTheme.primaryBlue,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Commentaire du professeur',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryBlue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  grade.feedback!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Color _getTypeColor(GradeType type) {
    switch (type) {
      case GradeType.evaluation:
        return AppTheme.primaryBlue;
      case GradeType.homework:
        return AppTheme.secondaryBlue;
      case GradeType.participation:
        return AppTheme.successGreen;
      case GradeType.project:
        return AppTheme.goodPurple;
      case GradeType.exam:
        return AppTheme.accentRed;
      case GradeType.quiz:
        return AppTheme.warningOrange;
    }
  }

  IconData _getTypeIcon(GradeType type) {
    switch (type) {
      case GradeType.evaluation:
        return Icons.assignment;
      case GradeType.homework:
        return Icons.home_work;
      case GradeType.participation:
        return Icons.record_voice_over;
      case GradeType.project:
        return Icons.folder_special;
      case GradeType.exam:
        return Icons.quiz;
      case GradeType.quiz:
        return Icons.help_outline;
    }
  }
}
