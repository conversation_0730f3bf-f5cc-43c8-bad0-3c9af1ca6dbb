import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/behavior_note.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';
import '../../widgets/behavior_note_card.dart';

class BehaviorNotesScreen extends StatefulWidget {
  const BehaviorNotesScreen({super.key});

  @override
  State<BehaviorNotesScreen> createState() => _BehaviorNotesScreenState();
}

class _BehaviorNotesScreenState extends State<BehaviorNotesScreen> {
  String _searchQuery = '';
  BehaviorType? _selectedType;
  String? _selectedStudentId;
  DateTime? _selectedDate;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notes de comportement'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filtrer',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.feedbackTemplates);
            },
            tooltip: 'Gérer les modèles',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              _buildFilterSummary(appProvider),
              _buildQuickStats(appProvider),
              Expanded(child: _buildBehaviorNotesList(appProvider)),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(
            context,
            AppRouter.addBehaviorNote,
            arguments: <String, dynamic>{},
          );
        },
        icon: const Icon(Icons.note_add),
        label: const Text('Ajouter une note'),
      ),
    );
  }

  Widget _buildFilterSummary(AppProvider appProvider) {
    if (_searchQuery.isEmpty &&
        _selectedType == null &&
        _selectedStudentId == null &&
        _selectedDate == null) {
      return const SizedBox.shrink();
    }

    final selectedStudent = _selectedStudentId != null
        ? appProvider.getStudent(_selectedStudentId!)
        : null;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          if (_searchQuery.isNotEmpty)
            _buildFilterChip('Recherche: $_searchQuery', () {
              setState(() {
                _searchQuery = '';
              });
            }),
          if (_selectedType != null)
            _buildFilterChip(_getTypeDisplayName(_selectedType!), () {
              setState(() {
                _selectedType = null;
              });
            }),
          if (selectedStudent != null)
            _buildFilterChip('Élève: ${selectedStudent.fullName}', () {
              setState(() {
                _selectedStudentId = null;
              });
            }),
          if (_selectedDate != null)
            _buildFilterChip(
              'Date: ${DateFormat('dd/MM/yyyy').format(_selectedDate!)}',
              () {
                setState(() {
                  _selectedDate = null;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Chip(
      label: Text(label),
      deleteIcon: const Icon(Icons.close, size: 18),
      onDeleted: onRemove,
      backgroundColor: Theme.of(context).colorScheme.surface,
    );
  }

  Widget _buildQuickStats(AppProvider appProvider) {
    final filteredNotes = _getFilteredBehaviorNotes(appProvider);
    final positiveCount = filteredNotes
        .where((n) => n.type == BehaviorType.positive)
        .length;
    final negativeCount = filteredNotes
        .where((n) => n.type == BehaviorType.negative)
        .length;
    final neutralCount = filteredNotes
        .where((n) => n.type == BehaviorType.neutral)
        .length;

    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'Positives',
            positiveCount.toString(),
            AppTheme.successGreen,
            Icons.thumb_up,
          ),
          _buildStatItem(
            'Négatives',
            negativeCount.toString(),
            AppTheme.accentRed,
            Icons.thumb_down,
          ),
          _buildStatItem(
            'Neutres',
            neutralCount.toString(),
            AppTheme.neutralGray,
            Icons.remove,
          ),
          _buildStatItem(
            'Total',
            filteredNotes.length.toString(),
            AppTheme.primaryBlue,
            Icons.note,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  Widget _buildBehaviorNotesList(AppProvider appProvider) {
    final filteredNotes = _getFilteredBehaviorNotes(appProvider);

    if (filteredNotes.isEmpty) {
      return _buildEmptyState();
    }

    // Group notes by date
    final groupedNotes = <String, List<BehaviorNote>>{};
    for (final note in filteredNotes) {
      final dateKey = DateFormat('yyyy-MM-dd').format(note.date);
      groupedNotes.putIfAbsent(dateKey, () => []).add(note);
    }

    final sortedDates = groupedNotes.keys.toList()
      ..sort((a, b) => b.compareTo(a)); // Most recent first

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateKey = sortedDates[index];
        final date = DateTime.parse(dateKey);
        final notes = groupedNotes[dateKey]!;

        return _buildDateGroup(date, notes, appProvider);
      },
    );
  }

  Widget _buildDateGroup(
    DateTime date,
    List<BehaviorNote> notes,
    AppProvider appProvider,
  ) {
    final dateFormat = DateFormat('EEEE d MMMM yyyy', 'fr_FR');
    final isToday = _isSameDay(date, DateTime.now());

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    dateFormat.format(date),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (isToday)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.successGreen,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Aujourd\'hui',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                Text(
                  '${notes.length} note${notes.length > 1 ? 's' : ''}',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          // Notes list
          ...notes.map(
            (note) => BehaviorNoteCard(
              behaviorNote: note,
              student: appProvider.getStudent(note.studentId),
              onEdit: () => _editBehaviorNote(note),
              onDelete: () => _deleteBehaviorNote(note, appProvider),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.note_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucune note de comportement',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Commencez par ajouter des notes de comportement',
            style: TextStyle(fontSize: 16, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'Utilisez le bouton + en bas à droite',
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrer les notes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Search field
            TextField(
              decoration: const InputDecoration(
                labelText: 'Rechercher',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('Filtres avancés à implémenter'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  List<BehaviorNote> _getFilteredBehaviorNotes(AppProvider appProvider) {
    var notes = appProvider.behaviorNotes;

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      notes = notes.where((note) {
        final student = appProvider.getStudent(note.studentId);
        return student?.fullName.toLowerCase().contains(_searchQuery) == true ||
            note.customNote?.toLowerCase().contains(_searchQuery) == true ||
            note.displayTags.any(
              (tag) => tag.toLowerCase().contains(_searchQuery),
            );
      }).toList();
    }

    // Filter by type
    if (_selectedType != null) {
      notes = notes.where((note) => note.type == _selectedType).toList();
    }

    // Filter by student
    if (_selectedStudentId != null) {
      notes = notes
          .where((note) => note.studentId == _selectedStudentId)
          .toList();
    }

    // Filter by date
    if (_selectedDate != null) {
      notes = notes
          .where((note) => _isSameDay(note.date, _selectedDate!))
          .toList();
    }

    // Sort by date (most recent first)
    notes.sort((a, b) => b.date.compareTo(a.date));

    return notes;
  }

  String _getTypeDisplayName(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return 'Positif';
      case BehaviorType.negative:
        return 'Négatif';
      case BehaviorType.neutral:
        return 'Neutre';
    }
  }

  void _editBehaviorNote(BehaviorNote note) {
    Navigator.pushNamed(
      context,
      AppRouter.addBehaviorNote,
      arguments: {'behaviorNoteId': note.id, 'studentId': note.studentId},
    );
  }

  void _deleteBehaviorNote(BehaviorNote note, AppProvider appProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la note'),
        content: const Text(
          'Êtes-vous sûr de vouloir supprimer cette note de comportement ? '
          'Cette action ne peut pas être annulée.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await appProvider.deleteBehaviorNote(note.id);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Note supprimée'),
                      backgroundColor: AppTheme.successGreen,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la suppression: $e'),
                      backgroundColor: AppTheme.accentRed,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}
