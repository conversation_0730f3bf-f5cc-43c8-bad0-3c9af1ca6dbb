import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';

class AddSubjectScreen extends StatefulWidget {
  const AddSubjectScreen({super.key});

  @override
  State<AddSubjectScreen> createState() => _AddSubjectScreenState();
}

class _AddSubjectScreenState extends State<AddSubjectScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _descriptionController = TextEditingController();

  CourseCategory _selectedCategory = CourseCategory.autre;
  Color _selectedColor = Colors.blue;

  final List<Color> _availableColors = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.red,
    Colors.purple,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.amber,
    Colors.cyan,
    Colors.lime,
    Colors.deepOrange,
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Ajouter une matière'),
        actions: [
          TextButton(onPressed: _saveSubject, child: const Text('Enregistrer')),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildCategorySection(),
              const SizedBox(height: 24),
              _buildColorSection(),
              const SizedBox(height: 24),
              _buildDescriptionSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de base',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la matière *',
                prefixIcon: Icon(Icons.subject),
                hintText: 'Ex: Mathématiques',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom de la matière est requis';
                }
                return null;
              },
              onChanged: (value) {
                // Auto-generate code from name
                if (_codeController.text.isEmpty ||
                    _codeController.text ==
                        _generateCode(_nameController.text)) {
                  _codeController.text = _generateCode(value);
                }
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Code de la matière *',
                prefixIcon: Icon(Icons.tag),
                hintText: 'Ex: MATH',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le code de la matière est requis';
                }
                if (value.trim().length > 10) {
                  return 'Le code ne peut pas dépasser 10 caractères';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Catégorie',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CourseCategory>(
              value: _selectedCategory,
              isExpanded: true,
              decoration: const InputDecoration(
                labelText: 'Catégorie de la matière *',
                prefixIcon: Icon(Icons.category),
              ),
              items: CourseCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(_getCategoryIcon(category)),
                      const SizedBox(width: 8),
                      Text(_getCourseCategoryName(category)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                  });
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Couleur d\'affichage',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: _availableColors.map((color) {
                final isSelected = _selectedColor == color;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedColor = color;
                    });
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(color: Colors.black, width: 3)
                          : null,
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 20)
                        : null,
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Description',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description de la matière',
                prefixIcon: Icon(Icons.description),
                hintText: 'Décrivez brièvement cette matière...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  String _generateCode(String name) {
    if (name.isEmpty) return '';

    // Remove accents and special characters, take first letters of words
    final words = name
        .replaceAll(RegExp(r'[àáâãäå]'), 'a')
        .replaceAll(RegExp(r'[èéêë]'), 'e')
        .replaceAll(RegExp(r'[ìíîï]'), 'i')
        .replaceAll(RegExp(r'[òóôõö]'), 'o')
        .replaceAll(RegExp(r'[ùúûü]'), 'u')
        .replaceAll(RegExp(r'[ç]'), 'c')
        .replaceAll(RegExp(r'[^a-zA-Z\s]'), '')
        .split(' ')
        .where((word) => word.isNotEmpty)
        .toList();

    if (words.isEmpty) return '';

    if (words.length == 1) {
      return words[0]
          .substring(0, words[0].length > 4 ? 4 : words[0].length)
          .toUpperCase();
    } else {
      return words.map((word) => word[0]).join().toUpperCase();
    }
  }

  void _saveSubject() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final subject = Subject(
        name: _nameController.text.trim(),
        code: _codeController.text.trim().toUpperCase(),
        category: _selectedCategory,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        color:
            '#${_selectedColor.value.toRadixString(16).substring(2).toUpperCase()}',
      );

      await context.read<AppProvider>().addSubject(subject);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Matière ajoutée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  IconData _getCategoryIcon(CourseCategory category) {
    switch (category) {
      case CourseCategory.languesFrancaises:
        return Icons.menu_book;
      case CourseCategory.mathematiques:
        return Icons.calculate;
      case CourseCategory.sciences:
        return Icons.science;
      case CourseCategory.histoireGeographie:
        return Icons.public;
      case CourseCategory.languesVivantes:
        return Icons.language;
      case CourseCategory.arts:
        return Icons.palette;
      case CourseCategory.eps:
        return Icons.sports;
      case CourseCategory.technologie:
        return Icons.computer;
      case CourseCategory.educationCivique:
        return Icons.gavel;
      case CourseCategory.philosophie:
        return Icons.psychology;
      case CourseCategory.specialites:
        return Icons.star;
      case CourseCategory.autre:
        return Icons.subject;
    }
  }

  String _getCourseCategoryName(CourseCategory category) {
    switch (category) {
      case CourseCategory.languesFrancaises:
        return 'Français';
      case CourseCategory.mathematiques:
        return 'Mathématiques';
      case CourseCategory.sciences:
        return 'Sciences';
      case CourseCategory.histoireGeographie:
        return 'Histoire-Géographie';
      case CourseCategory.languesVivantes:
        return 'Langues vivantes';
      case CourseCategory.arts:
        return 'Arts';
      case CourseCategory.eps:
        return 'EPS';
      case CourseCategory.technologie:
        return 'Technologie';
      case CourseCategory.educationCivique:
        return 'Éducation civique';
      case CourseCategory.philosophie:
        return 'Philosophie';
      case CourseCategory.specialites:
        return 'Spécialités';
      case CourseCategory.autre:
        return 'Autre';
    }
  }
}
