import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'rubric.g.dart';

@HiveType(typeId: 10)
class RubricCriterion extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name;

  @HiveField(2)
  String? description;

  @HiveField(3)
  late double maxPoints;

  @HiveField(4)
  late double weight; // Coefficient/pondération

  @HiveField(5)
  late List<String> levels; // Niveaux de performance

  @HiveField(6)
  late List<String> levelDescriptions; // Descriptions des niveaux

  @HiveField(7)
  late List<double> levelPoints; // Points pour chaque niveau

  RubricCriterion({
    String? id,
    required this.name,
    this.description,
    required this.maxPoints,
    this.weight = 1.0,
    List<String>? levels,
    List<String>? levelDescriptions,
    List<double>? levelPoints,
  }) {
    this.id = id ?? const Uuid().v4();
    this.levels = levels ?? ['Insuffisant', 'Passable', 'Bien', 'Excellent'];
    this.levelDescriptions = levelDescriptions ?? ['', '', '', ''];
    this.levelPoints = levelPoints ?? [0, maxPoints * 0.5, maxPoints * 0.75, maxPoints];
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'maxPoints': maxPoints,
    'weight': weight,
    'levels': levels,
    'levelDescriptions': levelDescriptions,
    'levelPoints': levelPoints,
  };

  factory RubricCriterion.fromJson(Map<String, dynamic> json) => RubricCriterion(
    id: json['id'],
    name: json['name'],
    description: json['description'],
    maxPoints: json['maxPoints'].toDouble(),
    weight: json['weight']?.toDouble() ?? 1.0,
    levels: List<String>.from(json['levels']),
    levelDescriptions: List<String>.from(json['levelDescriptions']),
    levelPoints: List<double>.from(json['levelPoints'].map((x) => x.toDouble())),
  );
}

@HiveType(typeId: 11)
class Rubric extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name;

  @HiveField(2)
  String? description;

  @HiveField(3)
  late String subject;

  @HiveField(4)
  late List<RubricCriterion> criteria;

  @HiveField(5)
  late DateTime createdAt;

  @HiveField(6)
  late DateTime updatedAt;

  @HiveField(7)
  bool isTemplate; // Si c'est un modèle réutilisable

  @HiveField(8)
  late double totalMaxPoints;

  Rubric({
    String? id,
    required this.name,
    this.description,
    required this.subject,
    List<RubricCriterion>? criteria,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isTemplate = false,
  }) {
    this.id = id ?? const Uuid().v4();
    this.criteria = criteria ?? [];
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    _calculateTotalMaxPoints();
  }

  void _calculateTotalMaxPoints() {
    totalMaxPoints = criteria.fold(0.0, (sum, criterion) => sum + (criterion.maxPoints * criterion.weight));
  }

  void addCriterion(RubricCriterion criterion) {
    criteria.add(criterion);
    _calculateTotalMaxPoints();
    updateTimestamp();
  }

  void removeCriterion(String criterionId) {
    criteria.removeWhere((c) => c.id == criterionId);
    _calculateTotalMaxPoints();
    updateTimestamp();
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  // Convert total points to grade out of 20
  double convertToGrade(double totalPoints) {
    if (totalMaxPoints == 0) return 0;
    return (totalPoints / totalMaxPoints) * 20.0;
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'subject': subject,
    'criteria': criteria.map((c) => c.toJson()).toList(),
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'isTemplate': isTemplate,
    'totalMaxPoints': totalMaxPoints,
  };

  factory Rubric.fromJson(Map<String, dynamic> json) => Rubric(
    id: json['id'],
    name: json['name'],
    description: json['description'],
    subject: json['subject'],
    criteria: (json['criteria'] as List).map((c) => RubricCriterion.fromJson(c)).toList(),
    createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    isTemplate: json['isTemplate'] ?? false,
  );
}

@HiveType(typeId: 12)
class RubricAssessment extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String studentId;

  @HiveField(2)
  late String rubricId;

  @HiveField(3)
  late String assessmentTitle;

  @HiveField(4)
  late DateTime date;

  @HiveField(5)
  late Map<String, int> criterionScores; // criterionId -> level index

  @HiveField(6)
  late Map<String, String> criterionComments; // criterionId -> comment

  @HiveField(7)
  String? generalComment;

  @HiveField(8)
  late DateTime createdAt;

  @HiveField(9)
  late DateTime updatedAt;

  @HiveField(10)
  late double totalPoints;

  @HiveField(11)
  late double finalGrade; // Note sur 20

  RubricAssessment({
    String? id,
    required this.studentId,
    required this.rubricId,
    required this.assessmentTitle,
    required this.date,
    Map<String, int>? criterionScores,
    Map<String, String>? criterionComments,
    this.generalComment,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.totalPoints = 0.0,
    this.finalGrade = 0.0,
  }) {
    this.id = id ?? const Uuid().v4();
    this.criterionScores = criterionScores ?? {};
    this.criterionComments = criterionComments ?? {};
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  void updateScore(String criterionId, int levelIndex, {String? comment}) {
    criterionScores[criterionId] = levelIndex;
    if (comment != null) {
      criterionComments[criterionId] = comment;
    }
    updateTimestamp();
  }

  void calculateTotalScore(Rubric rubric) {
    totalPoints = 0.0;
    for (final criterion in rubric.criteria) {
      final levelIndex = criterionScores[criterion.id];
      if (levelIndex != null && levelIndex < criterion.levelPoints.length) {
        totalPoints += criterion.levelPoints[levelIndex] * criterion.weight;
      }
    }
    finalGrade = rubric.convertToGrade(totalPoints);
    updateTimestamp();
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  String get displayGrade => '${finalGrade.toStringAsFixed(1)}/20';

  Map<String, dynamic> toJson() => {
    'id': id,
    'studentId': studentId,
    'rubricId': rubricId,
    'assessmentTitle': assessmentTitle,
    'date': date.toIso8601String(),
    'criterionScores': criterionScores,
    'criterionComments': criterionComments,
    'generalComment': generalComment,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'totalPoints': totalPoints,
    'finalGrade': finalGrade,
  };

  factory RubricAssessment.fromJson(Map<String, dynamic> json) => RubricAssessment(
    id: json['id'],
    studentId: json['studentId'],
    rubricId: json['rubricId'],
    assessmentTitle: json['assessmentTitle'],
    date: DateTime.parse(json['date']),
    criterionScores: Map<String, int>.from(json['criterionScores']),
    criterionComments: Map<String, String>.from(json['criterionComments']),
    generalComment: json['generalComment'],
    createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    totalPoints: json['totalPoints']?.toDouble() ?? 0.0,
    finalGrade: json['finalGrade']?.toDouble() ?? 0.0,
  );
}
