import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'behavior_note.g.dart';

@HiveType(typeId: 4)
enum BehaviorType {
  @HiveField(0)
  positive,
  @HiveField(1)
  negative,
  @HiveField(2)
  neutral,
}

@HiveType(typeId: 5)
enum PredefinedBehaviorTag {
  @HiveField(0)
  late,
  @HiveField(1)
  participated,
  @HiveField(2)
  homeworkMissing,
  @HiveField(3)
  homeworkComplete,
  @HiveField(4)
  disruptive,
  @HiveField(5)
  helpful,
  @HiveField(6)
  excellent,
  @HiveField(7)
  needsImprovement,
  @HiveField(8)
  absent,
  @HiveField(9)
  sick,
  @HiveField(10)
  goodEffort,
  @HiveField(11)
  lackOfEffort,
  @HiveField(12)
  creative,
  @HiveField(13)
  leadership,
  @HiveField(14)
  teamwork,
}

@HiveType(typeId: 6)
class BehaviorNote extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String studentId;

  @HiveField(2)
  late DateTime date;

  @HiveField(3)
  late BehaviorType type;

  @HiveField(4)
  List<PredefinedBehaviorTag>? predefinedTags;

  @HiveField(5)
  String? customNote;

  @HiveField(6)
  String? subject; // Matière

  @HiveField(7)
  late DateTime createdAt;

  @HiveField(8)
  late DateTime updatedAt;

  @HiveField(9)
  bool isImportant;

  @HiveField(10)
  String? lessonId; // Optional: link to specific lesson

  BehaviorNote({
    String? id,
    required this.studentId,
    required this.date,
    required this.type,
    this.predefinedTags,
    this.customNote,
    this.subject,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isImportant = false,
    this.lessonId,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    predefinedTags = predefinedTags ?? [];
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  String get typeDisplayName {
    switch (type) {
      case BehaviorType.positive:
        return 'Positif';
      case BehaviorType.negative:
        return 'Négatif';
      case BehaviorType.neutral:
        return 'Neutre';
    }
  }

  String getTagDisplayName(PredefinedBehaviorTag tag) {
    switch (tag) {
      case PredefinedBehaviorTag.late:
        return 'En retard';
      case PredefinedBehaviorTag.participated:
        return 'A participé';
      case PredefinedBehaviorTag.homeworkMissing:
        return 'Devoir manquant';
      case PredefinedBehaviorTag.homeworkComplete:
        return 'Devoir fait';
      case PredefinedBehaviorTag.disruptive:
        return 'Perturbateur';
      case PredefinedBehaviorTag.helpful:
        return 'Serviable';
      case PredefinedBehaviorTag.excellent:
        return 'Excellent';
      case PredefinedBehaviorTag.needsImprovement:
        return 'À améliorer';
      case PredefinedBehaviorTag.absent:
        return 'Absent';
      case PredefinedBehaviorTag.sick:
        return 'Malade';
      case PredefinedBehaviorTag.goodEffort:
        return 'Bon effort';
      case PredefinedBehaviorTag.lackOfEffort:
        return 'Manque d\'effort';
      case PredefinedBehaviorTag.creative:
        return 'Créatif';
      case PredefinedBehaviorTag.leadership:
        return 'Leadership';
      case PredefinedBehaviorTag.teamwork:
        return 'Travail d\'équipe';
    }
  }

  List<String> get displayTags {
    return predefinedTags?.map((tag) => getTagDisplayName(tag)).toList() ?? [];
  }

  String get summary {
    final tags = displayTags.join(', ');
    if (customNote != null && customNote!.isNotEmpty) {
      return tags.isNotEmpty ? '$tags - $customNote' : customNote!;
    }
    return tags;
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'studentId': studentId,
    'date': date.toIso8601String(),
    'type': type.index,
    'predefinedTags': predefinedTags?.map((tag) => tag.index).toList(),
    'customNote': customNote,
    'subject': subject,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'isImportant': isImportant,
    'lessonId': lessonId,
  };

  factory BehaviorNote.fromJson(Map<String, dynamic> json) => BehaviorNote(
    id: json['id'],
    studentId: json['studentId'],
    date: DateTime.parse(json['date']),
    type: BehaviorType.values[json['type']],
    predefinedTags: json['predefinedTags'] != null
        ? (json['predefinedTags'] as List)
              .map((i) => PredefinedBehaviorTag.values[i])
              .toList()
        : null,
    customNote: json['customNote'],
    subject: json['subject'],
    createdAt: json['createdAt'] != null
        ? DateTime.parse(json['createdAt'])
        : null,
    updatedAt: json['updatedAt'] != null
        ? DateTime.parse(json['updatedAt'])
        : null,
    isImportant: json['isImportant'] ?? false,
    lessonId: json['lessonId'],
  );
}
