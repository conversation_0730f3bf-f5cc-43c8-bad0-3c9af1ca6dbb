import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'attendance.g.dart';

@HiveType(typeId: 1)
enum AttendanceStatus {
  @HiveField(0)
  present,
  @HiveField(1)
  absent,
  @HiveField(2)
  late,
  @HiveField(3)
  excused,
  @HiveField(4)
  sick,
}

@HiveType(typeId: 2)
class AttendanceRecord extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String studentId;

  @HiveField(2)
  late DateTime date;

  @HiveField(3)
  late AttendanceStatus status;

  @HiveField(4)
  String? notes;

  @HiveField(5)
  late DateTime createdAt;

  @HiveField(6)
  late DateTime updatedAt;

  @HiveField(7)
  String? lessonId; // Optional: link to specific lesson

  AttendanceRecord({
    String? id,
    required this.studentId,
    required this.date,
    required this.status,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.lessonId,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  String get statusDisplayName {
    switch (status) {
      case AttendanceStatus.present:
        return 'Présent';
      case AttendanceStatus.absent:
        return 'Absent';
      case AttendanceStatus.late:
        return 'En retard';
      case AttendanceStatus.excused:
        return 'Excusé';
      case AttendanceStatus.sick:
        return 'Malade';
    }
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'studentId': studentId,
    'date': date.toIso8601String(),
    'status': status.index,
    'notes': notes,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'lessonId': lessonId,
  };

  factory AttendanceRecord.fromJson(Map<String, dynamic> json) => AttendanceRecord(
    id: json['id'],
    studentId: json['studentId'],
    date: DateTime.parse(json['date']),
    status: AttendanceStatus.values[json['status']],
    notes: json['notes'],
    createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    lessonId: json['lessonId'],
  );
}

@HiveType(typeId: 3)
class DailyAttendance extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late DateTime date;

  @HiveField(2)
  late List<AttendanceRecord> records;

  @HiveField(3)
  late DateTime createdAt;

  @HiveField(4)
  late DateTime updatedAt;

  @HiveField(5)
  bool isCompleted;

  DailyAttendance({
    String? id,
    required this.date,
    List<AttendanceRecord>? records,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isCompleted = false,
  }) {
    this.id = id ?? const Uuid().v4();
    this.records = records ?? [];
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  void addRecord(AttendanceRecord record) {
    records.add(record);
    updateTimestamp();
  }

  void updateRecord(String studentId, AttendanceStatus status, {String? notes}) {
    final existingIndex = records.indexWhere((r) => r.studentId == studentId);
    if (existingIndex != -1) {
      records[existingIndex].status = status;
      records[existingIndex].notes = notes;
      records[existingIndex].updateTimestamp();
    } else {
      addRecord(AttendanceRecord(
        studentId: studentId,
        date: date,
        status: status,
        notes: notes,
      ));
    }
    updateTimestamp();
  }

  AttendanceRecord? getRecordForStudent(String studentId) {
    try {
      return records.firstWhere((r) => r.studentId == studentId);
    } catch (e) {
      return null;
    }
  }

  int get presentCount => records.where((r) => r.status == AttendanceStatus.present).length;
  int get absentCount => records.where((r) => r.status == AttendanceStatus.absent).length;
  int get lateCount => records.where((r) => r.status == AttendanceStatus.late).length;
  int get excusedCount => records.where((r) => r.status == AttendanceStatus.excused).length;
  int get sickCount => records.where((r) => r.status == AttendanceStatus.sick).length;

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'date': date.toIso8601String(),
    'records': records.map((r) => r.toJson()).toList(),
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'isCompleted': isCompleted,
  };

  factory DailyAttendance.fromJson(Map<String, dynamic> json) => DailyAttendance(
    id: json['id'],
    date: DateTime.parse(json['date']),
    records: (json['records'] as List).map((r) => AttendanceRecord.fromJson(r)).toList(),
    createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    isCompleted: json['isCompleted'] ?? false,
  );
}
