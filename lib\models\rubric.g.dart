// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rubric.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class RubricCriterionAdapter extends TypeAdapter<RubricCriterion> {
  @override
  final int typeId = 10;

  @override
  RubricCriterion read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RubricCriterion(
      id: fields[0] as String?,
      name: fields[1] as String,
      description: fields[2] as String?,
      maxPoints: fields[3] as double,
      weight: fields[4] as double,
      levels: (fields[5] as List?)?.cast<String>(),
      levelDescriptions: (fields[6] as List?)?.cast<String>(),
      levelPoints: (fields[7] as List?)?.cast<double>(),
    );
  }

  @override
  void write(BinaryWriter writer, RubricCriterion obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.maxPoints)
      ..writeByte(4)
      ..write(obj.weight)
      ..writeByte(5)
      ..write(obj.levels)
      ..writeByte(6)
      ..write(obj.levelDescriptions)
      ..writeByte(7)
      ..write(obj.levelPoints);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RubricCriterionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RubricAdapter extends TypeAdapter<Rubric> {
  @override
  final int typeId = 11;

  @override
  Rubric read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Rubric(
      id: fields[0] as String?,
      name: fields[1] as String,
      description: fields[2] as String?,
      subject: fields[3] as String,
      criteria: (fields[4] as List?)?.cast<RubricCriterion>(),
      createdAt: fields[5] as DateTime?,
      updatedAt: fields[6] as DateTime?,
      isTemplate: fields[7] as bool,
    )..totalMaxPoints = fields[8] as double;
  }

  @override
  void write(BinaryWriter writer, Rubric obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.subject)
      ..writeByte(4)
      ..write(obj.criteria)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.updatedAt)
      ..writeByte(7)
      ..write(obj.isTemplate)
      ..writeByte(8)
      ..write(obj.totalMaxPoints);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RubricAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RubricAssessmentAdapter extends TypeAdapter<RubricAssessment> {
  @override
  final int typeId = 12;

  @override
  RubricAssessment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RubricAssessment(
      id: fields[0] as String?,
      studentId: fields[1] as String,
      rubricId: fields[2] as String,
      assessmentTitle: fields[3] as String,
      date: fields[4] as DateTime,
      criterionScores: (fields[5] as Map?)?.cast<String, int>(),
      criterionComments: (fields[6] as Map?)?.cast<String, String>(),
      generalComment: fields[7] as String?,
      createdAt: fields[8] as DateTime?,
      updatedAt: fields[9] as DateTime?,
      totalPoints: fields[10] as double,
      finalGrade: fields[11] as double,
    );
  }

  @override
  void write(BinaryWriter writer, RubricAssessment obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.rubricId)
      ..writeByte(3)
      ..write(obj.assessmentTitle)
      ..writeByte(4)
      ..write(obj.date)
      ..writeByte(5)
      ..write(obj.criterionScores)
      ..writeByte(6)
      ..write(obj.criterionComments)
      ..writeByte(7)
      ..write(obj.generalComment)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt)
      ..writeByte(10)
      ..write(obj.totalPoints)
      ..writeByte(11)
      ..write(obj.finalGrade);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RubricAssessmentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
