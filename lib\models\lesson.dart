import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'lesson.g.dart';

@HiveType(typeId: 13)
enum LessonStatus {
  @HiveField(0)
  planned,
  @HiveField(1)
  inProgress,
  @HiveField(2)
  completed,
  @HiveField(3)
  cancelled,
}

@HiveType(typeId: 14)
class LessonAttachment extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String name;

  @HiveField(2)
  late String path; // Local file path

  @HiveField(3)
  late String type; // pdf, image, link, etc.

  @HiveField(4)
  String? description;

  @HiveField(5)
  late DateTime addedAt;

  LessonAttachment({
    String? id,
    required this.name,
    required this.path,
    required this.type,
    this.description,
    DateTime? addedAt,
  }) {
    this.id = id ?? const Uuid().v4();
    this.addedAt = addedAt ?? DateTime.now();
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'path': path,
    'type': type,
    'description': description,
    'addedAt': addedAt.toIso8601String(),
  };

  factory LessonAttachment.fromJson(Map<String, dynamic> json) =>
      LessonAttachment(
        id: json['id'],
        name: json['name'],
        path: json['path'],
        type: json['type'],
        description: json['description'],
        addedAt: json['addedAt'] != null
            ? DateTime.parse(json['addedAt'])
            : null,
      );
}

@HiveType(typeId: 15)
class Lesson extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String title;

  @HiveField(2)
  late String subject;

  @HiveField(3)
  String? description;

  @HiveField(4)
  late DateTime startTime;

  @HiveField(5)
  late DateTime endTime;

  @HiveField(6)
  late LessonStatus status;

  @HiveField(7)
  String? classroom;

  @HiveField(8)
  List<String>? objectives; // Objectifs pédagogiques

  @HiveField(9)
  String? content; // Contenu du cours

  @HiveField(10)
  String? homework; // Devoirs à donner

  @HiveField(11)
  List<LessonAttachment>? attachments;

  @HiveField(12)
  String? notes; // Notes personnelles du professeur

  @HiveField(13)
  late DateTime createdAt;

  @HiveField(14)
  late DateTime updatedAt;

  @HiveField(15)
  List<String>? studentIds; // Élèves présents (optionnel)

  @HiveField(16)
  bool isRecurring; // Si c'est un cours récurrent

  @HiveField(17)
  String? recurringPattern; // Pattern de récurrence

  Lesson({
    String? id,
    required this.title,
    required this.subject,
    this.description,
    required this.startTime,
    required this.endTime,
    this.status = LessonStatus.planned,
    this.classroom,
    this.objectives,
    this.content,
    this.homework,
    this.attachments,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.studentIds,
    this.isRecurring = false,
    this.recurringPattern,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
    objectives = objectives ?? [];
    attachments = attachments ?? [];
    studentIds = studentIds ?? [];
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  Duration get duration => endTime.difference(startTime);

  String get durationString {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    if (hours > 0) {
      return '${hours}h${minutes > 0 ? ' ${minutes}min' : ''}';
    }
    return '${minutes}min';
  }

  String get statusDisplayName {
    switch (status) {
      case LessonStatus.planned:
        return 'Planifié';
      case LessonStatus.inProgress:
        return 'En cours';
      case LessonStatus.completed:
        return 'Terminé';
      case LessonStatus.cancelled:
        return 'Annulé';
    }
  }

  String get timeRange {
    final startStr =
        '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
    final endStr =
        '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';
    return '$startStr - $endStr';
  }

  bool get isToday {
    final now = DateTime.now();
    return startTime.year == now.year &&
        startTime.month == now.month &&
        startTime.day == now.day;
  }

  bool get isUpcoming {
    return startTime.isAfter(DateTime.now());
  }

  bool get isPast {
    return endTime.isBefore(DateTime.now());
  }

  void addAttachment(LessonAttachment attachment) {
    attachments ??= [];
    attachments!.add(attachment);
    updateTimestamp();
  }

  void removeAttachment(String attachmentId) {
    attachments?.removeWhere((a) => a.id == attachmentId);
    updateTimestamp();
  }

  void addObjective(String objective) {
    objectives ??= [];
    objectives!.add(objective);
    updateTimestamp();
  }

  void removeObjective(String objective) {
    objectives?.remove(objective);
    updateTimestamp();
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'subject': subject,
    'description': description,
    'startTime': startTime.toIso8601String(),
    'endTime': endTime.toIso8601String(),
    'status': status.index,
    'classroom': classroom,
    'objectives': objectives,
    'content': content,
    'homework': homework,
    'attachments': attachments?.map((a) => a.toJson()).toList(),
    'notes': notes,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'studentIds': studentIds,
    'isRecurring': isRecurring,
    'recurringPattern': recurringPattern,
  };

  factory Lesson.fromJson(Map<String, dynamic> json) => Lesson(
    id: json['id'],
    title: json['title'],
    subject: json['subject'],
    description: json['description'],
    startTime: DateTime.parse(json['startTime']),
    endTime: DateTime.parse(json['endTime']),
    status: LessonStatus.values[json['status']],
    classroom: json['classroom'],
    objectives: json['objectives'] != null
        ? List<String>.from(json['objectives'])
        : null,
    content: json['content'],
    homework: json['homework'],
    attachments: json['attachments'] != null
        ? (json['attachments'] as List)
              .map((a) => LessonAttachment.fromJson(a))
              .toList()
        : null,
    notes: json['notes'],
    createdAt: json['createdAt'] != null
        ? DateTime.parse(json['createdAt'])
        : null,
    updatedAt: json['updatedAt'] != null
        ? DateTime.parse(json['updatedAt'])
        : null,
    studentIds: json['studentIds'] != null
        ? List<String>.from(json['studentIds'])
        : null,
    isRecurring: json['isRecurring'] ?? false,
    recurringPattern: json['recurringPattern'],
  );
}
