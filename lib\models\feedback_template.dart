import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'feedback_template.g.dart';

@HiveType(typeId: 16)
enum FeedbackCategory {
  @HiveField(0)
  positive,
  @HiveField(1)
  constructive,
  @HiveField(2)
  encouragement,
  @HiveField(3)
  improvement,
  @HiveField(4)
  general,
}

@HiveType(typeId: 17)
class FeedbackTemplate extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String text;

  @HiveField(2)
  late FeedbackCategory category;

  @HiveField(3)
  String? subject; // Matière spécifique (optionnel)

  @HiveField(4)
  late DateTime createdAt;

  @HiveField(5)
  late DateTime updatedAt;

  @HiveField(6)
  int usageCount; // Nombre d'utilisations

  @HiveField(7)
  bool isDefault; // Si c'est un template par défaut

  @HiveField(8)
  bool isActive; // Si le template est actif

  FeedbackTemplate({
    String? id,
    required this.text,
    required this.category,
    this.subject,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.usageCount = 0,
    this.isDefault = false,
    this.isActive = true,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  void incrementUsage() {
    usageCount++;
    updateTimestamp();
  }

  String get categoryDisplayName {
    switch (category) {
      case FeedbackCategory.positive:
        return 'Positif';
      case FeedbackCategory.constructive:
        return 'Constructif';
      case FeedbackCategory.encouragement:
        return 'Encouragement';
      case FeedbackCategory.improvement:
        return 'À améliorer';
      case FeedbackCategory.general:
        return 'Général';
    }
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'text': text,
    'category': category.index,
    'subject': subject,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'usageCount': usageCount,
    'isDefault': isDefault,
    'isActive': isActive,
  };

  factory FeedbackTemplate.fromJson(Map<String, dynamic> json) => FeedbackTemplate(
    id: json['id'],
    text: json['text'],
    category: FeedbackCategory.values[json['category']],
    subject: json['subject'],
    createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    usageCount: json['usageCount'] ?? 0,
    isDefault: json['isDefault'] ?? false,
    isActive: json['isActive'] ?? true,
  );

  // Predefined French feedback templates
  static List<FeedbackTemplate> getDefaultTemplates() {
    return [
      // Positive feedback
      FeedbackTemplate(
        text: 'Excellent travail !',
        category: FeedbackCategory.positive,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Très bien travaillé !',
        category: FeedbackCategory.positive,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Bravo pour tes efforts !',
        category: FeedbackCategory.positive,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Parfait, continue comme ça !',
        category: FeedbackCategory.positive,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Très bonne participation !',
        category: FeedbackCategory.positive,
        isDefault: true,
      ),

      // Constructive feedback
      FeedbackTemplate(
        text: 'À revoir, mais bon effort.',
        category: FeedbackCategory.constructive,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Travail correct, peut mieux faire.',
        category: FeedbackCategory.constructive,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Bien, mais attention aux détails.',
        category: FeedbackCategory.constructive,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Bonne compréhension, à approfondir.',
        category: FeedbackCategory.constructive,
        isDefault: true,
      ),

      // Encouragement
      FeedbackTemplate(
        text: 'Continue tes efforts !',
        category: FeedbackCategory.encouragement,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Tu progresses bien !',
        category: FeedbackCategory.encouragement,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Ne te décourage pas !',
        category: FeedbackCategory.encouragement,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Tu es sur la bonne voie !',
        category: FeedbackCategory.encouragement,
        isDefault: true,
      ),

      // Improvement needed
      FeedbackTemplate(
        text: 'À revoir complètement.',
        category: FeedbackCategory.improvement,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Travail insuffisant.',
        category: FeedbackCategory.improvement,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Plus d\'efforts nécessaires.',
        category: FeedbackCategory.improvement,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Voir moi après le cours.',
        category: FeedbackCategory.improvement,
        isDefault: true,
      ),

      // General
      FeedbackTemplate(
        text: 'Devoir rendu en retard.',
        category: FeedbackCategory.general,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Absent lors de l\'évaluation.',
        category: FeedbackCategory.general,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Travail non rendu.',
        category: FeedbackCategory.general,
        isDefault: true,
      ),
      FeedbackTemplate(
        text: 'Bonne présentation.',
        category: FeedbackCategory.general,
        isDefault: true,
      ),
    ];
  }
}
