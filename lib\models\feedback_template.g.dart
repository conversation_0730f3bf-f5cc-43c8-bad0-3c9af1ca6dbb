// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'feedback_template.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FeedbackTemplateAdapter extends TypeAdapter<FeedbackTemplate> {
  @override
  final int typeId = 17;

  @override
  FeedbackTemplate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FeedbackTemplate(
      id: fields[0] as String?,
      text: fields[1] as String,
      category: fields[2] as FeedbackCategory,
      subject: fields[3] as String?,
      createdAt: fields[4] as DateTime?,
      updatedAt: fields[5] as DateTime?,
      usageCount: fields[6] as int,
      isDefault: fields[7] as bool,
      isActive: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, FeedbackTemplate obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.text)
      ..writeByte(2)
      ..write(obj.category)
      ..writeByte(3)
      ..write(obj.subject)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.updatedAt)
      ..writeByte(6)
      ..write(obj.usageCount)
      ..writeByte(7)
      ..write(obj.isDefault)
      ..writeByte(8)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FeedbackTemplateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FeedbackCategoryAdapter extends TypeAdapter<FeedbackCategory> {
  @override
  final int typeId = 16;

  @override
  FeedbackCategory read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return FeedbackCategory.positive;
      case 1:
        return FeedbackCategory.constructive;
      case 2:
        return FeedbackCategory.encouragement;
      case 3:
        return FeedbackCategory.improvement;
      case 4:
        return FeedbackCategory.general;
      default:
        return FeedbackCategory.positive;
    }
  }

  @override
  void write(BinaryWriter writer, FeedbackCategory obj) {
    switch (obj) {
      case FeedbackCategory.positive:
        writer.writeByte(0);
        break;
      case FeedbackCategory.constructive:
        writer.writeByte(1);
        break;
      case FeedbackCategory.encouragement:
        writer.writeByte(2);
        break;
      case FeedbackCategory.improvement:
        writer.writeByte(3);
        break;
      case FeedbackCategory.general:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FeedbackCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
