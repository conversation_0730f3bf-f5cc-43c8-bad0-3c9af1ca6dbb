import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'student.g.dart';

@HiveType(typeId: 0)
class Student extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String firstName;

  @HiveField(2)
  late String lastName;

  @HiveField(3)
  String? photoPath;

  @HiveField(4)
  DateTime? dateOfBirth;

  @HiveField(5)
  String? parentEmail;

  @HiveField(6)
  String? parentPhone;

  @HiveField(7)
  String? notes;

  @HiveField(8)
  late DateTime createdAt;

  @HiveField(9)
  late DateTime updatedAt;

  @HiveField(10)
  bool isActive;

  Student({
    String? id,
    required this.firstName,
    required this.lastName,
    this.photoPath,
    this.dateOfBirth,
    this.parentEmail,
    this.parentPhone,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.isActive = true,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  String get fullName => '$firstName $lastName';

  String get initials => '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';

  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month || 
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  @override
  String toString() => fullName;

  Map<String, dynamic> toJson() => {
    'id': id,
    'firstName': firstName,
    'lastName': lastName,
    'photoPath': photoPath,
    'dateOfBirth': dateOfBirth?.toIso8601String(),
    'parentEmail': parentEmail,
    'parentPhone': parentPhone,
    'notes': notes,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'isActive': isActive,
  };

  factory Student.fromJson(Map<String, dynamic> json) => Student(
    id: json['id'],
    firstName: json['firstName'],
    lastName: json['lastName'],
    photoPath: json['photoPath'],
    dateOfBirth: json['dateOfBirth'] != null ? DateTime.parse(json['dateOfBirth']) : null,
    parentEmail: json['parentEmail'],
    parentPhone: json['parentPhone'],
    notes: json['notes'],
    createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    isActive: json['isActive'] ?? true,
  );
}
