// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AppSettingsAdapter extends TypeAdapter<AppSettings> {
  @override
  final int typeId = 10;

  @override
  AppSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AppSettings(
      isDarkMode: fields[0] as bool,
      language: fields[1] as String,
      enableNotifications: fields[2] as bool,
      autoBackup: fields[3] as bool,
      backupFrequency: fields[4] as int,
      showWelcomeScreen: fields[5] as bool,
      dateFormat: fields[6] as String,
      timeFormat: fields[7] as String,
      enableSounds: fields[8] as bool,
      enableVibration: fields[9] as bool,
      fontSize: fields[10] as double,
      defaultGradingScale: fields[11] as String,
      showStudentPhotos: fields[12] as bool,
      enableQuickActions: fields[13] as bool,
      enableGestures: fields[14] as bool,
      exportFormat: fields[15] as String,
      enableAnalytics: fields[16] as bool,
      enableCrashReporting: fields[17] as bool,
      teacherName: fields[18] as String,
      schoolName: fields[19] as String,
      academicYear: fields[20] as String,
    );
  }

  @override
  void write(BinaryWriter writer, AppSettings obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.isDarkMode)
      ..writeByte(1)
      ..write(obj.language)
      ..writeByte(2)
      ..write(obj.enableNotifications)
      ..writeByte(3)
      ..write(obj.autoBackup)
      ..writeByte(4)
      ..write(obj.backupFrequency)
      ..writeByte(5)
      ..write(obj.showWelcomeScreen)
      ..writeByte(6)
      ..write(obj.dateFormat)
      ..writeByte(7)
      ..write(obj.timeFormat)
      ..writeByte(8)
      ..write(obj.enableSounds)
      ..writeByte(9)
      ..write(obj.enableVibration)
      ..writeByte(10)
      ..write(obj.fontSize)
      ..writeByte(11)
      ..write(obj.defaultGradingScale)
      ..writeByte(12)
      ..write(obj.showStudentPhotos)
      ..writeByte(13)
      ..write(obj.enableQuickActions)
      ..writeByte(14)
      ..write(obj.enableGestures)
      ..writeByte(15)
      ..write(obj.exportFormat)
      ..writeByte(16)
      ..write(obj.enableAnalytics)
      ..writeByte(17)
      ..write(obj.enableCrashReporting)
      ..writeByte(18)
      ..write(obj.teacherName)
      ..writeByte(19)
      ..write(obj.schoolName)
      ..writeByte(20)
      ..write(obj.academicYear);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
