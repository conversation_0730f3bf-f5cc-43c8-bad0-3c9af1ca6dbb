import 'package:flutter/material.dart';
import 'dart:io';
import '../models/student.dart';
import '../models/attendance.dart';
import '../utils/app_theme.dart';

class AttendanceStudentCard extends StatelessWidget {
  final Student student;
  final AttendanceRecord? attendanceRecord;
  final Function(AttendanceStatus) onStatusChanged;
  final bool isCompleted;

  const AttendanceStudentCard({
    super.key,
    required this.student,
    this.attendanceRecord,
    required this.onStatusChanged,
    this.isCompleted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                _buildAvatar(),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStudentInfo(),
                ),
                if (attendanceRecord != null)
                  _buildCurrentStatus(),
              ],
            ),
            const SizedBox(height: 16),
            _buildAttendanceButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 25,
      backgroundColor: AppTheme.primaryBlue.withValues(alpha: 0.1),
      backgroundImage: student.photoPath != null && File(student.photoPath!).existsSync()
          ? FileImage(File(student.photoPath!))
          : null,
      child: student.photoPath == null || !File(student.photoPath!).existsSync()
          ? Text(
              student.initials,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryBlue,
              ),
            )
          : null,
    );
  }

  Widget _buildStudentInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          student.fullName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (student.age != null)
          Text(
            '${student.age} ans',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
      ],
    );
  }

  Widget _buildCurrentStatus() {
    if (attendanceRecord == null) return const SizedBox.shrink();

    final status = attendanceRecord!.status;
    final color = _getStatusColor(status);
    final icon = _getStatusIcon(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            attendanceRecord!.statusDisplayName,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceButtons() {
    return Row(
      children: [
        Expanded(
          child: _buildStatusButton(
            AttendanceStatus.present,
            'Présent',
            Icons.check_circle,
            AppTheme.successGreen,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatusButton(
            AttendanceStatus.absent,
            'Absent',
            Icons.cancel,
            AppTheme.accentRed,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatusButton(
            AttendanceStatus.late,
            'Retard',
            Icons.access_time,
            AppTheme.warningOrange,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatusButton(
            AttendanceStatus.excused,
            'Excusé',
            Icons.info,
            AppTheme.secondaryBlue,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusButton(
    AttendanceStatus status,
    String label,
    IconData icon,
    Color color,
  ) {
    final isSelected = attendanceRecord?.status == status;
    final isDisabled = isCompleted;

    return Material(
      color: isSelected ? color : Colors.transparent,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        onTap: isDisabled ? null : () => onStatusChanged(status),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? color : color.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20,
                color: isSelected ? Colors.white : color,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(AttendanceStatus status) {
    switch (status) {
      case AttendanceStatus.present:
        return AppTheme.successGreen;
      case AttendanceStatus.absent:
        return AppTheme.accentRed;
      case AttendanceStatus.late:
        return AppTheme.warningOrange;
      case AttendanceStatus.excused:
        return AppTheme.secondaryBlue;
      case AttendanceStatus.sick:
        return AppTheme.neutralGray;
    }
  }

  IconData _getStatusIcon(AttendanceStatus status) {
    switch (status) {
      case AttendanceStatus.present:
        return Icons.check_circle;
      case AttendanceStatus.absent:
        return Icons.cancel;
      case AttendanceStatus.late:
        return Icons.access_time;
      case AttendanceStatus.excused:
        return Icons.info;
      case AttendanceStatus.sick:
        return Icons.local_hospital;
    }
  }
}
