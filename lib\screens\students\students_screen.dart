import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/student.dart';
import '../../utils/app_router.dart';
import '../../widgets/student_card.dart';

class StudentsScreen extends StatefulWidget {
  const StudentsScreen({super.key});

  @override
  State<StudentsScreen> createState() => _StudentsScreenState();
}

class _StudentsScreenState extends State<StudentsScreen> {
  String _searchQuery = '';
  bool _sortByName = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Élèves'),
        actions: [
          IconButton(
            icon: Icon(_sortByName ? Icons.sort_by_alpha : Icons.access_time),
            onPressed: () {
              setState(() {
                _sortByName = !_sortByName;
              });
            },
            tooltip: _sortByName ? 'Trier par date' : 'Trier par nom',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final students = _getFilteredAndSortedStudents(
            appProvider.activeStudents,
          );

          if (students.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              _buildSearchBar(),
              _buildStudentStats(students),
              Expanded(child: _buildStudentsList(students)),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRouter.addStudent);
        },
        tooltip: 'Ajouter un élève',
        child: const Icon(Icons.person_add),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: const InputDecoration(
          hintText: 'Rechercher un élève...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  Widget _buildStudentStats(List<Student> students) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            icon: Icons.people,
            label: 'Total',
            value: students.length.toString(),
          ),
          _buildStatItem(
            icon: Icons.person,
            label: 'Actifs',
            value: students.where((s) => s.isActive).length.toString(),
          ),
          _buildStatItem(
            icon: Icons.cake,
            label: 'Anniversaires',
            value: _getBirthdaysThisMonth(students).toString(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Icon(icon, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildStudentsList(List<Student> students) {
    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: students.length,
      itemBuilder: (context, index) {
        final student = students[index];
        return StudentCard(
          student: student,
          onTap: () {
            Navigator.pushNamed(
              context,
              AppRouter.studentDetail,
              arguments: student.id,
            );
          },
          onEdit: () {
            Navigator.pushNamed(
              context,
              AppRouter.editStudent,
              arguments: student.id,
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'Aucun élève',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Commencez par ajouter vos premiers élèves',
            style: TextStyle(fontSize: 16, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'Utilisez le bouton + en bas à droite',
            style: TextStyle(fontSize: 14, color: Colors.grey[400]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<Student> _getFilteredAndSortedStudents(List<Student> students) {
    // Filter by search query
    var filtered = students.where((student) {
      if (_searchQuery.isEmpty) return true;
      return student.fullName.toLowerCase().contains(_searchQuery) ||
          student.firstName.toLowerCase().contains(_searchQuery) ||
          student.lastName.toLowerCase().contains(_searchQuery);
    }).toList();

    // Sort students
    if (_sortByName) {
      filtered.sort((a, b) => a.fullName.compareTo(b.fullName));
    } else {
      filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    }

    return filtered;
  }

  int _getBirthdaysThisMonth(List<Student> students) {
    final now = DateTime.now();
    return students.where((student) {
      if (student.dateOfBirth == null) return false;
      return student.dateOfBirth!.month == now.month;
    }).length;
  }
}
