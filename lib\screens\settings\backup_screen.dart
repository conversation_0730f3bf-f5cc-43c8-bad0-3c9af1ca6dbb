import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../services/license_service.dart';
import '../../services/backup_service.dart';
import '../../services/database_service.dart';
import '../../utils/app_theme.dart';

class BackupScreen extends StatefulWidget {
  const BackupScreen({super.key});

  @override
  State<BackupScreen> createState() => _BackupScreenState();
}

class _BackupScreenState extends State<BackupScreen> {
  bool _isExporting = false;
  bool _isRestoring = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Sauvegarde et Export')),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          final isLicensed = appProvider.isLicensed;

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!isLicensed) ...[
                  Card(
                    color: Colors.orange[50],
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          const Icon(
                            Icons.lock,
                            size: 48,
                            color: Colors.orange,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Fonctionnalité Premium',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'L\'export et la sauvegarde des données nécessitent la version complète de l\'application.',
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: () =>
                                LicenseService.showRestrictionDialog(
                                  context,
                                  'export_data',
                                ),
                            icon: const Icon(Icons.coffee),
                            label: const Text('Acheter un café ☕'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.backup),
                            SizedBox(width: 8),
                            Text(
                              'Sauvegarde automatique',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        ListTile(
                          title: const Text(
                            'Activer la sauvegarde automatique',
                          ),
                          subtitle: Text(
                            isLicensed
                                ? 'Sauvegarde automatique des données'
                                : 'Fonctionnalité premium',
                          ),
                          trailing: isLicensed
                              ? Switch(
                                  value: appProvider.settings.autoBackup,
                                  onChanged: (value) {
                                    appProvider.updateSetting(
                                      'autoBackup',
                                      value,
                                    );
                                  },
                                )
                              : const Icon(Icons.lock, color: Colors.orange),
                          onTap: !isLicensed
                              ? () => LicenseService.showRestrictionDialog(
                                  context,
                                  'backup_restore',
                                )
                              : null,
                        ),
                        if (isLicensed && appProvider.settings.autoBackup)
                          ListTile(
                            title: const Text('Fréquence'),
                            subtitle: Text(
                              'Tous les ${appProvider.settings.backupFrequency} jours',
                            ),
                            trailing: DropdownButton<int>(
                              value: appProvider.settings.backupFrequency,
                              items: [1, 3, 7, 14, 30].map((days) {
                                return DropdownMenuItem(
                                  value: days,
                                  child: Text(
                                    '$days jour${days > 1 ? 's' : ''}',
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  appProvider.updateSetting(
                                    'backupFrequency',
                                    value,
                                  );
                                }
                              },
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.file_download),
                            SizedBox(width: 8),
                            Text(
                              'Export des données',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        ListTile(
                          title: const Text('Exporter les élèves'),
                          subtitle: Text(
                            isLicensed
                                ? 'Exporter la liste des élèves'
                                : 'Fonctionnalité premium',
                          ),
                          leading: const Icon(Icons.people),
                          trailing: isLicensed
                              ? const Icon(Icons.arrow_forward_ios)
                              : const Icon(Icons.lock, color: Colors.orange),
                          onTap: () {
                            if (isLicensed) {
                              _exportStudents();
                            } else {
                              LicenseService.showRestrictionDialog(
                                context,
                                'export_data',
                              );
                            }
                          },
                        ),
                        ListTile(
                          title: const Text('Exporter les notes'),
                          subtitle: Text(
                            isLicensed
                                ? 'Exporter toutes les notes'
                                : 'Fonctionnalité premium',
                          ),
                          leading: const Icon(Icons.grade),
                          trailing: isLicensed
                              ? const Icon(Icons.arrow_forward_ios)
                              : const Icon(Icons.lock, color: Colors.orange),
                          onTap: () {
                            if (isLicensed) {
                              _exportGrades();
                            } else {
                              LicenseService.showRestrictionDialog(
                                context,
                                'export_data',
                              );
                            }
                          },
                        ),
                        ListTile(
                          title: const Text('Exporter les présences'),
                          subtitle: Text(
                            isLicensed
                                ? 'Exporter les données de présence'
                                : 'Fonctionnalité premium',
                          ),
                          leading: const Icon(Icons.event_available),
                          trailing: isLicensed
                              ? const Icon(Icons.arrow_forward_ios)
                              : const Icon(Icons.lock, color: Colors.orange),
                          onTap: () {
                            if (isLicensed) {
                              _exportAttendance();
                            } else {
                              LicenseService.showRestrictionDialog(
                                context,
                                'export_data',
                              );
                            }
                          },
                        ),
                        ListTile(
                          title: const Text('Export complet'),
                          subtitle: Text(
                            isLicensed
                                ? 'Exporter toutes les données'
                                : 'Fonctionnalité premium',
                          ),
                          leading: const Icon(Icons.archive),
                          trailing: isLicensed
                              ? (_isExporting
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Icon(Icons.arrow_forward_ios))
                              : const Icon(Icons.lock, color: Colors.orange),
                          onTap: () {
                            if (isLicensed && !_isExporting) {
                              _exportAll();
                            } else if (!isLicensed) {
                              LicenseService.showRestrictionDialog(
                                context,
                                'export_data',
                              );
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                // Restore Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.restore, color: Colors.blue),
                            SizedBox(width: 8),
                            Text(
                              'Restauration',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        ListTile(
                          title: const Text('Restaurer depuis un fichier'),
                          subtitle: Text(
                            isLicensed
                                ? 'Importer une sauvegarde complète'
                                : 'Fonctionnalité premium',
                          ),
                          leading: const Icon(Icons.file_upload),
                          trailing: isLicensed
                              ? (_isRestoring
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Icon(Icons.arrow_forward_ios))
                              : const Icon(Icons.lock, color: Colors.orange),
                          onTap: () {
                            if (isLicensed && !_isRestoring) {
                              _restoreFromFile();
                            } else if (!isLicensed) {
                              LicenseService.showRestrictionDialog(
                                context,
                                'backup_restore',
                              );
                            }
                          },
                        ),
                        const Divider(),
                        ListTile(
                          title: const Text('Réinitialiser toutes les données'),
                          subtitle: const Text('⚠️ Action irréversible'),
                          leading: const Icon(
                            Icons.delete_forever,
                            color: Colors.red,
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: _showResetConfirmation,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _exportStudents() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Export des élèves en cours...')),
      );

      final filePath = await BackupService.exportSpecificData('students');

      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export terminé: ${filePath.split('/').last}'),
            backgroundColor: AppTheme.successGreen,
            action: SnackBarAction(
              label: 'Partager',
              onPressed: () => BackupService.shareBackup(),
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de l\'export'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _exportGrades() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Export des notes en cours...')),
      );

      final filePath = await BackupService.exportSpecificData('grades');

      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export terminé: ${filePath.split('/').last}'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de l\'export'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _exportAttendance() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Export des présences en cours...')),
      );

      final filePath = await BackupService.exportSpecificData('attendance');

      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export terminé: ${filePath.split('/').last}'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de l\'export'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _exportAll() async {
    setState(() {
      _isExporting = true;
    });

    try {
      final success = await BackupService.shareBackup();

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Sauvegarde complète créée et partagée!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de la création de la sauvegarde'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'export: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _restoreFromFile() async {
    setState(() {
      _isRestoring = true;
    });

    try {
      final success = await BackupService.pickAndRestoreBackup();

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Restauration terminée avec succès!'),
              backgroundColor: Colors.green,
            ),
          );
          // Refresh the app provider to reload data
          if (mounted) {
            context.read<AppProvider>().initializeApp();
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erreur lors de la restauration'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRestoring = false;
        });
      }
    }
  }

  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⚠️ Confirmation'),
        content: const Text(
          'Cette action supprimera définitivement toutes vos données:\n\n'
          '• Tous les élèves\n'
          '• Toutes les notes\n'
          '• Toutes les présences\n'
          '• Tous les cours\n'
          '• Toutes les notes de comportement\n\n'
          'Cette action est irréversible. Êtes-vous sûr(e) ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _resetAllData();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer tout'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetAllData() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Suppression des données en cours...')),
      );

      // Clear all data from database
      await DatabaseService.clearAllData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Toutes les données ont été supprimées'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh the app provider
        context.read<AppProvider>().initializeApp();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la suppression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
