import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/behavior_note.dart';
import '../../utils/app_theme.dart';

class AddBehaviorNoteScreen extends StatefulWidget {
  final String? studentId;
  final String? lessonId;
  final String? behaviorNoteId;

  const AddBehaviorNoteScreen({
    super.key,
    this.studentId,
    this.lessonId,
    this.behaviorNoteId,
  });

  @override
  State<AddBehaviorNoteScreen> createState() => _AddBehaviorNoteScreenState();
}

class _AddBehaviorNoteScreenState extends State<AddBehaviorNoteScreen> {
  final _formKey = GlobalKey<FormState>();
  final _customNoteController = TextEditingController();
  final _subjectController = TextEditingController();

  String? _selectedStudentId;
  BehaviorType _selectedType = BehaviorType.neutral;
  List<PredefinedBehaviorTag> _selectedTags = [];
  DateTime _selectedDate = DateTime.now();
  bool _isImportant = false;
  bool _isLoading = false;
  BehaviorNote? _existingNote;

  @override
  void initState() {
    super.initState();
    _selectedStudentId = widget.studentId;
    _loadExistingNote();
  }

  void _loadExistingNote() {
    if (widget.behaviorNoteId != null) {
      final appProvider = context.read<AppProvider>();
      _existingNote = appProvider.behaviorNotes
          .where((note) => note.id == widget.behaviorNoteId)
          .firstOrNull;

      if (_existingNote != null) {
        _selectedStudentId = _existingNote!.studentId;
        _selectedType = _existingNote!.type;
        _selectedTags = List.from(_existingNote!.predefinedTags ?? []);
        _customNoteController.text = _existingNote!.customNote ?? '';
        _subjectController.text = _existingNote!.subject ?? '';
        _selectedDate = _existingNote!.date;
        _isImportant = _existingNote!.isImportant;
      }
    }
  }

  @override
  void dispose() {
    _customNoteController.dispose();
    _subjectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = _existingNote != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Modifier la note' : 'Ajouter une note'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveNote,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Enregistrer'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildStudentSelection(),
            const SizedBox(height: 24),
            _buildTypeSelection(),
            const SizedBox(height: 24),
            _buildPredefinedTags(),
            const SizedBox(height: 24),
            _buildCustomNote(),
            const SizedBox(height: 24),
            _buildMetadata(),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentSelection() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final students = appProvider.activeStudents;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Élève',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedStudentId,
                  isExpanded: true,
                  decoration: const InputDecoration(
                    labelText: 'Sélectionner un élève *',
                    prefixIcon: Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez sélectionner un élève';
                    }
                    return null;
                  },
                  items: students.map((student) {
                    return DropdownMenuItem(
                      value: student.id,
                      child: Text(
                        student.fullName,
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStudentId = value;
                    });
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTypeSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Type de comportement',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTypeButton(
                    BehaviorType.positive,
                    'Positif',
                    Icons.thumb_up,
                    AppTheme.successGreen,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildTypeButton(
                    BehaviorType.negative,
                    'Négatif',
                    Icons.thumb_down,
                    AppTheme.accentRed,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildTypeButton(
                    BehaviorType.neutral,
                    'Neutre',
                    Icons.remove,
                    AppTheme.neutralGray,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeButton(
    BehaviorType type,
    String label,
    IconData icon,
    Color color,
  ) {
    final isSelected = _selectedType == type;

    return Material(
      color: isSelected ? color : Colors.transparent,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedType = type;
            // Clear tags when changing type to avoid conflicts
            _selectedTags.clear();
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? color : color.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Icon(icon, size: 24, color: isSelected ? Colors.white : color),
              const SizedBox(height: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? Colors.white : color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPredefinedTags() {
    final availableTags = _getTagsForType(_selectedType);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Tags prédéfinis',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Sélectionnez les comportements observés',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: availableTags.map((tag) => _buildTagChip(tag)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTagChip(PredefinedBehaviorTag tag) {
    final isSelected = _selectedTags.contains(tag);
    final color = _getTypeColor(_selectedType);
    final displayName = _getTagDisplayName(tag);

    return FilterChip(
      label: Text(displayName),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            _selectedTags.add(tag);
          } else {
            _selectedTags.remove(tag);
          }
        });
      },
      selectedColor: color.withValues(alpha: 0.2),
      checkmarkColor: color,
      side: BorderSide(color: isSelected ? color : Colors.grey[300]!),
    );
  }

  Widget _buildCustomNote() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Note personnalisée',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _customNoteController,
              decoration: const InputDecoration(
                labelText: 'Commentaire libre',
                hintText: 'Ajoutez des détails sur le comportement...',
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadata() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informations complémentaires',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _subjectController,
              decoration: const InputDecoration(
                labelText: 'Matière',
                hintText: 'Mathématiques, Français, etc.',
                prefixIcon: Icon(Icons.subject),
              ),
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Date',
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Marquer comme important'),
              subtitle: const Text(
                'Cette note nécessite une attention particulière',
              ),
              value: _isImportant,
              onChanged: (value) {
                setState(() {
                  _isImportant = value;
                });
              },
              activeColor: AppTheme.warningOrange,
            ),
          ],
        ),
      ),
    );
  }

  List<PredefinedBehaviorTag> _getTagsForType(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return [
          PredefinedBehaviorTag.participated,
          PredefinedBehaviorTag.homeworkComplete,
          PredefinedBehaviorTag.helpful,
          PredefinedBehaviorTag.excellent,
          PredefinedBehaviorTag.goodEffort,
          PredefinedBehaviorTag.creative,
          PredefinedBehaviorTag.leadership,
          PredefinedBehaviorTag.teamwork,
        ];
      case BehaviorType.negative:
        return [
          PredefinedBehaviorTag.late,
          PredefinedBehaviorTag.homeworkMissing,
          PredefinedBehaviorTag.disruptive,
          PredefinedBehaviorTag.needsImprovement,
          PredefinedBehaviorTag.lackOfEffort,
        ];
      case BehaviorType.neutral:
        return [PredefinedBehaviorTag.absent, PredefinedBehaviorTag.sick];
    }
  }

  Color _getTypeColor(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return AppTheme.successGreen;
      case BehaviorType.negative:
        return AppTheme.accentRed;
      case BehaviorType.neutral:
        return AppTheme.neutralGray;
    }
  }

  String _getTagDisplayName(PredefinedBehaviorTag tag) {
    // Use the method from BehaviorNote model
    final dummyNote = BehaviorNote(
      studentId: '',
      date: DateTime.now(),
      type: BehaviorType.neutral,
    );
    return dummyNote.getTagDisplayName(tag);
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveNote() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedTags.isEmpty && _customNoteController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
            'Veuillez sélectionner au moins un tag ou ajouter une note',
          ),
          backgroundColor: AppTheme.warningOrange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final behaviorNote = BehaviorNote(
        id: _existingNote?.id,
        studentId: _selectedStudentId!,
        date: _selectedDate,
        type: _selectedType,
        predefinedTags: _selectedTags.isEmpty ? null : _selectedTags,
        customNote: _customNoteController.text.trim().isEmpty
            ? null
            : _customNoteController.text.trim(),
        subject: _subjectController.text.trim().isEmpty
            ? null
            : _subjectController.text.trim(),
        isImportant: _isImportant,
        lessonId: widget.lessonId,
        createdAt: _existingNote?.createdAt,
      );

      final appProvider = context.read<AppProvider>();
      if (_existingNote != null) {
        await appProvider.updateBehaviorNote(behaviorNote);
      } else {
        await appProvider.addBehaviorNote(behaviorNote);
      }

      if (mounted) {
        final student = appProvider.getStudent(_selectedStudentId!);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _existingNote != null
                  ? 'Note modifiée pour ${student?.fullName}'
                  : 'Note ajoutée pour ${student?.fullName}',
            ),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'enregistrement: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
