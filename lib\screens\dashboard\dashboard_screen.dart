import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/behavior_note.dart';
import '../../models/attendance.dart';
import '../../services/database_service.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';
import '../../widgets/dashboard_card.dart';
import '../../widgets/quick_action_grid.dart';
import '../../widgets/recent_activity_list.dart';
import '../../widgets/class_overview_card.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Ensure data is loaded when dashboard opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppProvider>().initializeApp();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord'),
        actions: [
          IconButton(
            icon: const Icon(Icons.brightness_6),
            onPressed: () {
              context.read<AppProvider>().toggleTheme();
            },
            tooltip: 'Changer le thème',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.userManual);
            },
            tooltip: 'Manuel d\'utilisation',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.pushNamed(context, AppRouter.settings);
            },
            tooltip: 'Paramètres',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Chargement des données...'),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => appProvider.initializeApp(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeHeader(),
                  const SizedBox(height: 24),
                  _buildQuickStats(appProvider),
                  const SizedBox(height: 24),
                  QuickActionGrid(),
                  const SizedBox(height: 24),
                  ClassOverviewCard(),
                  const SizedBox(height: 24),
                  _buildTodaySection(appProvider),
                  const SizedBox(height: 24),
                  RecentActivityList(),
                  const SizedBox(height: 24),
                  _buildWeeklyAttendanceSummary(appProvider),
                  const SizedBox(height: 24),
                  _buildQuickInsights(appProvider),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: _buildQuickAddFAB(),
    );
  }

  Widget _buildWelcomeHeader() {
    final now = DateTime.now();
    final timeOfDay = _getTimeOfDayGreeting();
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppTheme.primaryBlue, AppTheme.secondaryBlue],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$timeOfDay !',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              dateFormat.format(now),
              style: const TextStyle(fontSize: 16, color: Colors.white70),
            ),
            const SizedBox(height: 12),
            const Text(
              'isoucklou',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white60,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(AppProvider appProvider) {
    final dashboardData = appProvider.getDashboardData();

    return Row(
      children: [
        Expanded(
          child: DashboardCard(
            title: 'Élèves',
            value: dashboardData['totalStudents'].toString(),
            icon: Icons.people,
            color: AppTheme.primaryGreen,
            onTap: () => Navigator.pushNamed(context, AppRouter.students),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: DashboardCard(
            title: 'Cours aujourd\'hui',
            value: dashboardData['todayLessons'].toString(),
            icon: Icons.schedule,
            color: AppTheme.successGreen,
            onTap: () => Navigator.pushNamed(context, AppRouter.lessons),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: DashboardCard(
            title: 'Notes récentes',
            value: dashboardData['recentGrades'].toString(),
            icon: Icons.grade,
            color: AppTheme.warningOrange,
            onTap: () => Navigator.pushNamed(context, AppRouter.grades),
          ),
        ),
      ],
    );
  }

  Widget _buildTodaySection(AppProvider appProvider) {
    final todayLessons = appProvider.getTodayLessons();
    final now = DateTime.now();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Aujourd\'hui',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () =>
                  Navigator.pushNamed(context, AppRouter.timetable),
              child: const Text('Voir l\'emploi du temps'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (todayLessons.isEmpty)
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  Icon(Icons.free_breakfast, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 12),
                  Text(
                    'Aucun cours prévu aujourd\'hui',
                    style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Profitez de cette journée libre !',
                    style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  ),
                ],
              ),
            ),
          )
        else
          ...todayLessons
              .take(3)
              .map(
                (lesson) => Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getLessonStatusColor(
                        lesson,
                        now,
                      ).withValues(alpha: 0.1),
                      child: Icon(
                        _getLessonStatusIcon(lesson, now),
                        color: _getLessonStatusColor(lesson, now),
                      ),
                    ),
                    title: Text(lesson.title),
                    subtitle: Text(
                      '${lesson.subject} • ${DateFormat('HH:mm').format(lesson.startTime)} - ${DateFormat('HH:mm').format(lesson.endTime)}',
                    ),
                    trailing: _getLessonStatusChip(lesson, now),
                    onTap: () => Navigator.pushNamed(
                      context,
                      AppRouter.lessonDetail,
                      arguments: lesson.id,
                    ),
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildWeeklyAttendanceSummary(AppProvider appProvider) {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));

    // Get all daily attendance records for the week
    final weeklyAttendanceRecords = <AttendanceRecord>[];
    for (int i = 0; i < 7; i++) {
      final date = weekStart.add(Duration(days: i));
      final dailyAttendance = DatabaseService.getDailyAttendance(date);
      if (dailyAttendance != null) {
        weeklyAttendanceRecords.addAll(dailyAttendance.records);
      }
    }

    final presentCount = weeklyAttendanceRecords
        .where((record) => record.status == AttendanceStatus.present)
        .length;
    final absentCount = weeklyAttendanceRecords
        .where((record) => record.status == AttendanceStatus.absent)
        .length;
    final lateCount = weeklyAttendanceRecords
        .where((record) => record.status == AttendanceStatus.late)
        .length;
    final excusedCount = weeklyAttendanceRecords
        .where((record) => record.status == AttendanceStatus.excused)
        .length;

    final totalRecords = weeklyAttendanceRecords.length;
    final attendanceRate = totalRecords > 0
        ? ((presentCount + lateCount) / totalRecords * 100).round()
        : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Présences cette semaine',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () =>
                  Navigator.pushNamed(context, AppRouter.attendanceHistory),
              child: const Text('Voir l\'historique'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildAttendanceStatItem(
                        'Taux de présence',
                        '$attendanceRate%',
                        Icons.trending_up,
                        attendanceRate >= 90
                            ? AppTheme.successGreen
                            : attendanceRate >= 75
                            ? AppTheme.warningOrange
                            : AppTheme.accentRed,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildAttendanceStatItem(
                        'Présents',
                        presentCount.toString(),
                        Icons.check_circle,
                        AppTheme.successGreen,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildAttendanceStatItem(
                        'Absents',
                        absentCount.toString(),
                        Icons.cancel,
                        AppTheme.accentRed,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildAttendanceStatItem(
                        'En retard',
                        lateCount.toString(),
                        Icons.access_time,
                        AppTheme.warningOrange,
                      ),
                    ),
                  ],
                ),
                if (excusedCount > 0) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildAttendanceStatItem(
                          'Excusés',
                          excusedCount.toString(),
                          Icons.verified,
                          AppTheme.secondaryBlue,
                        ),
                      ),
                      const Expanded(
                        child: SizedBox(),
                      ), // Empty space for alignment
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAttendanceStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickInsights(AppProvider appProvider) {
    final recentBehaviorNotes = appProvider.behaviorNotes
        .where(
          (note) => note.date.isAfter(
            DateTime.now().subtract(const Duration(days: 7)),
          ),
        )
        .toList();

    final positiveNotes = recentBehaviorNotes
        .where((n) => n.type == BehaviorType.positive)
        .length;
    final negativeNotes = recentBehaviorNotes
        .where((n) => n.type == BehaviorType.negative)
        .length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Aperçu de la semaine',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => _showDetailedAnalysis(appProvider),
              child: const Text('Voir l\'analyse'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.thumb_up,
                        color: AppTheme.successGreen,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        positiveNotes.toString(),
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.successGreen,
                        ),
                      ),
                      const Text(
                        'Notes positives',
                        style: TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.thumb_down,
                        color: AppTheme.accentRed,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        negativeNotes.toString(),
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.accentRed,
                        ),
                      ),
                      const Text(
                        'Notes négatives',
                        style: TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget? _buildQuickAddFAB() {
    return FloatingActionButton(
      onPressed: _showQuickAddMenu,
      tooltip: 'Ajout rapide',
      child: const Icon(Icons.add),
    );
  }

  void _showDetailedAnalysis(AppProvider appProvider) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Analyse détaillée',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    children: [
                      _buildAnalysisCard(
                        'Comportement des élèves',
                        _buildBehaviorAnalysis(appProvider),
                      ),
                      const SizedBox(height: 16),
                      _buildAnalysisCard(
                        'Performance académique',
                        _buildGradeAnalysis(appProvider),
                      ),
                      const SizedBox(height: 16),
                      _buildAnalysisCard(
                        'Présences',
                        _buildAttendanceAnalysis(appProvider),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnalysisCard(String title, Widget content) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            content,
          ],
        ),
      ),
    );
  }

  Widget _buildBehaviorAnalysis(AppProvider appProvider) {
    final totalNotes = appProvider.behaviorNotes.length;
    final positiveNotes = appProvider.behaviorNotes
        .where((note) => note.type == BehaviorType.positive)
        .length;
    final negativeNotes = appProvider.behaviorNotes
        .where((note) => note.type == BehaviorType.negative)
        .length;
    final neutralNotes = appProvider.behaviorNotes
        .where((note) => note.type == BehaviorType.neutral)
        .length;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildAnalysisStatItem(
              'Positives',
              positiveNotes.toString(),
              AppTheme.successGreen,
              Icons.thumb_up,
            ),
            _buildAnalysisStatItem(
              'Négatives',
              negativeNotes.toString(),
              AppTheme.accentRed,
              Icons.thumb_down,
            ),
            _buildAnalysisStatItem(
              'Neutres',
              neutralNotes.toString(),
              AppTheme.neutralGray,
              Icons.remove,
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (totalNotes > 0)
          Text(
            'Ratio positif: ${((positiveNotes / totalNotes) * 100).toStringAsFixed(1)}%',
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
      ],
    );
  }

  Widget _buildGradeAnalysis(AppProvider appProvider) {
    final recentGrades = appProvider.grades
        .where(
          (grade) => grade.createdAt.isAfter(
            DateTime.now().subtract(const Duration(days: 30)),
          ),
        )
        .toList();

    if (recentGrades.isEmpty) {
      return const Text('Aucune note récente');
    }

    final averageGrade =
        recentGrades.map((g) => g.normalizedValue).reduce((a, b) => a + b) /
        recentGrades.length;

    final excellentGrades = recentGrades
        .where((grade) => grade.normalizedValue >= 16)
        .length;
    final goodGrades = recentGrades
        .where(
          (grade) => grade.normalizedValue >= 12 && grade.normalizedValue < 16,
        )
        .length;
    final poorGrades = recentGrades
        .where((grade) => grade.normalizedValue < 10)
        .length;

    return Column(
      children: [
        Text(
          'Moyenne générale: ${averageGrade.toStringAsFixed(1)}/20',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildAnalysisStatItem(
              'Excellentes',
              excellentGrades.toString(),
              AppTheme.successGreen,
              Icons.star,
            ),
            _buildAnalysisStatItem(
              'Bonnes',
              goodGrades.toString(),
              AppTheme.warningOrange,
              Icons.trending_up,
            ),
            _buildAnalysisStatItem(
              'Faibles',
              poorGrades.toString(),
              AppTheme.accentRed,
              Icons.trending_down,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAttendanceAnalysis(AppProvider appProvider) {
    // This would analyze attendance data
    return const Column(
      children: [
        Text('Analyse des présences'),
        SizedBox(height: 8),
        Text(
          'Fonctionnalité en cours de développement',
          style: TextStyle(fontStyle: FontStyle.italic),
        ),
      ],
    );
  }

  Widget _buildAnalysisStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  void _showQuickAddMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.8,
        minChildSize: 0.4,
        builder: (context, scrollController) {
          return Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Ajout rapide',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    children: [
                      ListTile(
                        leading: const Icon(Icons.person_add),
                        title: const Text('Ajouter un élève'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRouter.addStudent);
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.grade),
                        title: const Text('Ajouter une note'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                            context,
                            AppRouter.addGrade,
                            arguments: <String, dynamic>{},
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.note_add),
                        title: const Text('Note de comportement'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                            context,
                            AppRouter.addBehaviorNote,
                            arguments: <String, dynamic>{},
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.schedule),
                        title: const Text('Ajouter un cours'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRouter.addLesson);
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.school),
                        title: const Text('Créer un programme'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRouter.addCourse);
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.subject),
                        title: const Text('Ajouter une matière'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRouter.addSubject);
                        },
                      ),
                      const Divider(),
                      ListTile(
                        leading: const Icon(Icons.help_outline),
                        title: const Text('Manuel d\'utilisation'),
                        subtitle: const Text('Guide complet de l\'application'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, AppRouter.userManual);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getTimeOfDayGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  }

  Color _getLessonStatusColor(lesson, DateTime now) {
    if (now.isBefore(lesson.startTime)) return AppTheme.secondaryBlue;
    if (now.isAfter(lesson.endTime)) return AppTheme.neutralGray;
    return AppTheme.successGreen;
  }

  IconData _getLessonStatusIcon(lesson, DateTime now) {
    if (now.isBefore(lesson.startTime)) return Icons.schedule;
    if (now.isAfter(lesson.endTime)) return Icons.check_circle;
    return Icons.play_circle_filled;
  }

  Widget _getLessonStatusChip(lesson, DateTime now) {
    String status;
    Color color;

    if (now.isBefore(lesson.startTime)) {
      status = 'À venir';
      color = AppTheme.secondaryBlue;
    } else if (now.isAfter(lesson.endTime)) {
      status = 'Terminé';
      color = AppTheme.neutralGray;
    } else {
      status = 'En cours';
      color = AppTheme.successGreen;
    }

    return Chip(
      label: Text(
        status,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color),
    );
  }
}
