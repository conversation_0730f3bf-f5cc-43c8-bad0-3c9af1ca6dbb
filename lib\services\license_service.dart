import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';

class LicenseService {
  static const String _licenseKey = 'app_license_activated';
  static const String _activationDateKey = 'license_activation_date';
  static const String _paymentPhoneNumber = '+237674667234';
  static const String _developerEmail = '<EMAIL>';
  static const String _buyMeCoffeeMessage =
      'Bonjour! Je souhaite activer ma licence pour l\'application isoucklou. Merci!';

  static SharedPreferences? _prefs;

  // Initialize the service
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  // Check if the app is licensed
  static bool get isLicensed {
    return _prefs?.getBool(_licenseKey) ?? false;
  }

  // Get activation date
  static DateTime? get activationDate {
    final timestamp = _prefs?.getInt(_activationDateKey);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  // Activate license
  static Future<void> activateLicense() async {
    await _prefs?.setBool(_licenseKey, true);
    await _prefs?.setInt(
      _activationDateKey,
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  // Deactivate license (for testing purposes)
  static Future<void> deactivateLicense() async {
    await _prefs?.setBool(_licenseKey, false);
    await _prefs?.remove(_activationDateKey);
  }

  // Get payment phone number
  static String get paymentPhoneNumber => _paymentPhoneNumber;

  // Get developer email
  static String get developerEmail => _developerEmail;

  // Launch WhatsApp with payment message
  static Future<void> launchPaymentWhatsApp() async {
    final whatsappUrl =
        'https://wa.me/$_paymentPhoneNumber?text=${Uri.encodeComponent(_buyMeCoffeeMessage)}';

    try {
      final uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch WhatsApp';
      }
    } catch (e) {
      debugPrint('Error launching WhatsApp: $e');
      rethrow;
    }
  }

  // Launch SMS with payment message
  static Future<void> launchPaymentSMS() async {
    final smsUrl =
        'sms:$_paymentPhoneNumber?body=${Uri.encodeComponent(_buyMeCoffeeMessage)}';

    try {
      final uri = Uri.parse(smsUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw 'Could not launch SMS';
      }
    } catch (e) {
      debugPrint('Error launching SMS: $e');
      rethrow;
    }
  }

  // Launch phone call
  static Future<void> launchPaymentCall() async {
    final phoneUrl = 'tel:$_paymentPhoneNumber';

    try {
      final uri = Uri.parse(phoneUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw 'Could not launch phone call';
      }
    } catch (e) {
      debugPrint('Error launching phone call: $e');
      rethrow;
    }
  }

  // Get license status text
  static String get licenseStatusText {
    if (isLicensed) {
      final date = activationDate;
      if (date != null) {
        return 'Licence activée le ${date.day}/${date.month}/${date.year}';
      }
      return 'Licence activée';
    }
    return 'Version gratuite - Fonctionnalités limitées';
  }

  // Check if a feature is restricted
  static bool isFeatureRestricted(String featureName) {
    if (isLicensed) return false;

    // Define restricted features for free version
    const restrictedFeatures = [
      'export_data',
      'advanced_reports',
      'bulk_operations',
      'custom_themes',
      'backup_restore',
      'unlimited_students', // Limit to 10 students
      'unlimited_courses', // Limit to 5 courses
      'advanced_analytics',
    ];

    return restrictedFeatures.contains(featureName);
  }

  // Get student limit for free version
  static int get maxStudentsForFreeVersion => 10;

  // Get course limit for free version
  static int get maxCoursesForFreeVersion => 5;

  // Show restriction dialog
  static void showRestrictionDialog(BuildContext context, String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fonctionnalité Premium'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.lock, size: 48, color: Colors.orange),
            const SizedBox(height: 16),
            Text(
              'Cette fonctionnalité nécessite la version complète de l\'application.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'Soutenez le développement en achetant un café au développeur!',
              textAlign: TextAlign.center,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showPaymentOptions(context);
            },
            child: const Text('Acheter un café ☕'),
          ),
        ],
      ),
    );
  }

  // Show payment options dialog
  static void _showPaymentOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Acheter un café ☕'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Contactez le développeur pour activer votre licence:',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _paymentPhoneNumber,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Choisissez votre méthode de contact préférée:',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await launchPaymentWhatsApp();
              } catch (e) {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
              }
            },
            child: const Text('WhatsApp'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await launchPaymentSMS();
              } catch (e) {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
              }
            },
            child: const Text('SMS'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await launchPaymentCall();
              } catch (e) {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('Erreur: $e')));
              }
            },
            child: const Text('Appeler'),
          ),
        ],
      ),
    );
  }
}
