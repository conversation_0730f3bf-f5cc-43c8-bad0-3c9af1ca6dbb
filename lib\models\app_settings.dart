import 'package:hive/hive.dart';

part 'app_settings.g.dart';

@HiveType(typeId: 10)
class AppSettings extends HiveObject {
  @HiveField(0)
  bool isDarkMode;

  @HiveField(1)
  String language;

  @HiveField(2)
  bool enableNotifications;

  @HiveField(3)
  bool autoBackup;

  @HiveField(4)
  int backupFrequency; // in days

  @HiveField(5)
  bool showWelcomeScreen;

  @HiveField(6)
  String dateFormat;

  @HiveField(7)
  String timeFormat;

  @HiveField(8)
  bool enableSounds;

  @HiveField(9)
  bool enableVibration;

  @HiveField(10)
  double fontSize;

  @HiveField(11)
  String defaultGradingScale;

  @HiveField(12)
  bool showStudentPhotos;

  @HiveField(13)
  bool enableQuickActions;

  @HiveField(14)
  bool enableGestures;

  @HiveField(15)
  String exportFormat;

  @HiveField(16)
  bool enableAnalytics;

  @HiveField(17)
  bool enableCrashReporting;

  @HiveField(18)
  String teacherName;

  @HiveField(19)
  String schoolName;

  @HiveField(20)
  String academicYear;

  @HiveField(21)
  String? defaultSubject;

  @HiveField(22)
  String? defaultGradeTitle;

  AppSettings({
    this.isDarkMode = false,
    this.language = 'fr',
    this.enableNotifications = true,
    this.autoBackup = false,
    this.backupFrequency = 7,
    this.showWelcomeScreen = true,
    this.dateFormat = 'dd/MM/yyyy',
    this.timeFormat = 'HH:mm',
    this.enableSounds = true,
    this.enableVibration = true,
    this.fontSize = 14.0,
    this.defaultGradingScale = '0-20',
    this.showStudentPhotos = true,
    this.enableQuickActions = true,
    this.enableGestures = true,
    this.exportFormat = 'pdf',
    this.enableAnalytics = false,
    this.enableCrashReporting = false,
    this.teacherName = '',
    this.schoolName = '',
    this.academicYear = '',
    this.defaultSubject,
    this.defaultGradeTitle,
  });

  // Copy with method for immutable updates
  AppSettings copyWith({
    bool? isDarkMode,
    String? language,
    bool? enableNotifications,
    bool? autoBackup,
    int? backupFrequency,
    bool? showWelcomeScreen,
    String? dateFormat,
    String? timeFormat,
    bool? enableSounds,
    bool? enableVibration,
    double? fontSize,
    String? defaultGradingScale,
    bool? showStudentPhotos,
    bool? enableQuickActions,
    bool? enableGestures,
    String? exportFormat,
    bool? enableAnalytics,
    bool? enableCrashReporting,
    String? teacherName,
    String? schoolName,
    String? academicYear,
  }) {
    return AppSettings(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      language: language ?? this.language,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      autoBackup: autoBackup ?? this.autoBackup,
      backupFrequency: backupFrequency ?? this.backupFrequency,
      showWelcomeScreen: showWelcomeScreen ?? this.showWelcomeScreen,
      dateFormat: dateFormat ?? this.dateFormat,
      timeFormat: timeFormat ?? this.timeFormat,
      enableSounds: enableSounds ?? this.enableSounds,
      enableVibration: enableVibration ?? this.enableVibration,
      fontSize: fontSize ?? this.fontSize,
      defaultGradingScale: defaultGradingScale ?? this.defaultGradingScale,
      showStudentPhotos: showStudentPhotos ?? this.showStudentPhotos,
      enableQuickActions: enableQuickActions ?? this.enableQuickActions,
      enableGestures: enableGestures ?? this.enableGestures,
      exportFormat: exportFormat ?? this.exportFormat,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      enableCrashReporting: enableCrashReporting ?? this.enableCrashReporting,
      teacherName: teacherName ?? this.teacherName,
      schoolName: schoolName ?? this.schoolName,
      academicYear: academicYear ?? this.academicYear,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'isDarkMode': isDarkMode,
      'language': language,
      'enableNotifications': enableNotifications,
      'autoBackup': autoBackup,
      'backupFrequency': backupFrequency,
      'showWelcomeScreen': showWelcomeScreen,
      'dateFormat': dateFormat,
      'timeFormat': timeFormat,
      'enableSounds': enableSounds,
      'enableVibration': enableVibration,
      'fontSize': fontSize,
      'defaultGradingScale': defaultGradingScale,
      'showStudentPhotos': showStudentPhotos,
      'enableQuickActions': enableQuickActions,
      'enableGestures': enableGestures,
      'exportFormat': exportFormat,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'teacherName': teacherName,
      'schoolName': schoolName,
      'academicYear': academicYear,
    };
  }

  // Create from JSON
  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      isDarkMode: json['isDarkMode'] ?? false,
      language: json['language'] ?? 'fr',
      enableNotifications: json['enableNotifications'] ?? true,
      autoBackup: json['autoBackup'] ?? false,
      backupFrequency: json['backupFrequency'] ?? 7,
      showWelcomeScreen: json['showWelcomeScreen'] ?? true,
      dateFormat: json['dateFormat'] ?? 'dd/MM/yyyy',
      timeFormat: json['timeFormat'] ?? 'HH:mm',
      enableSounds: json['enableSounds'] ?? true,
      enableVibration: json['enableVibration'] ?? true,
      fontSize: json['fontSize']?.toDouble() ?? 14.0,
      defaultGradingScale: json['defaultGradingScale'] ?? '0-20',
      showStudentPhotos: json['showStudentPhotos'] ?? true,
      enableQuickActions: json['enableQuickActions'] ?? true,
      enableGestures: json['enableGestures'] ?? true,
      exportFormat: json['exportFormat'] ?? 'pdf',
      enableAnalytics: json['enableAnalytics'] ?? false,
      enableCrashReporting: json['enableCrashReporting'] ?? false,
      teacherName: json['teacherName'] ?? '',
      schoolName: json['schoolName'] ?? '',
      academicYear: json['academicYear'] ?? '',
    );
  }

  @override
  String toString() {
    return 'AppSettings(isDarkMode: $isDarkMode, language: $language, teacherName: $teacherName, schoolName: $schoolName)';
  }
}

// Enum for supported languages
enum SupportedLanguage {
  french('fr', 'Français'),
  english('en', 'English');

  const SupportedLanguage(this.code, this.displayName);
  final String code;
  final String displayName;
}

// Enum for date formats
enum DateFormat {
  ddMMyyyy('dd/MM/yyyy'),
  MMddyyyy('MM/dd/yyyy'),
  yyyyMMdd('yyyy-MM-dd');

  const DateFormat(this.format);
  final String format;
}

// Enum for time formats
enum TimeFormat {
  format24('HH:mm'),
  format12('hh:mm a');

  const TimeFormat(this.format);
  final String format;
}

// Enum for export formats
enum ExportFormat {
  pdf('pdf', 'PDF'),
  excel('xlsx', 'Excel'),
  csv('csv', 'CSV');

  const ExportFormat(this.extension, this.displayName);
  final String extension;
  final String displayName;
}

// Enum for grading scales
enum GradingScale {
  french('0-20', 'Français (0-20)'),
  american('A-F', 'Américain (A-F)'),
  percentage('0-100', 'Pourcentage (0-100)');

  const GradingScale(this.scale, this.displayName);
  final String scale;
  final String displayName;
}
