import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import '../models/behavior_note.dart';
import '../models/student.dart';
import '../utils/app_theme.dart';

class BehaviorNoteCard extends StatelessWidget {
  final BehaviorNote behaviorNote;
  final Student? student;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const BehaviorNoteCard({
    super.key,
    required this.behaviorNote,
    this.student,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 12),
            _buildContent(),
            if (behaviorNote.subject != null || behaviorNote.isImportant) ...[
              const SizedBox(height: 12),
              _buildMetadata(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        _buildStudentAvatar(),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                student?.fullName ?? 'Élève inconnu',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                DateFormat('HH:mm').format(behaviorNote.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        _buildTypeIndicator(),
        _buildActionMenu(),
      ],
    );
  }

  Widget _buildStudentAvatar() {
    return CircleAvatar(
      radius: 20,
      backgroundColor: AppTheme.primaryBlue.withValues(alpha: 0.1),
      backgroundImage: student?.photoPath != null && File(student!.photoPath!).existsSync()
          ? FileImage(File(student!.photoPath!))
          : null,
      child: student?.photoPath == null || !File(student!.photoPath!).existsSync()
          ? Text(
              student?.initials ?? '?',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryBlue,
              ),
            )
          : null,
    );
  }

  Widget _buildTypeIndicator() {
    final color = _getTypeColor(behaviorNote.type);
    final icon = _getTypeIcon(behaviorNote.type);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            behaviorNote.typeDisplayName,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionMenu() {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 20),
              SizedBox(width: 8),
              Text('Modifier'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 20, color: Colors.red),
              SizedBox(width: 8),
              Text('Supprimer', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
      child: const Icon(Icons.more_vert, size: 20),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Predefined tags
        if (behaviorNote.predefinedTags != null && behaviorNote.predefinedTags!.isNotEmpty)
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: behaviorNote.displayTags.map((tag) => _buildTag(tag)).toList(),
          ),
        
        // Custom note
        if (behaviorNote.customNote != null && behaviorNote.customNote!.isNotEmpty) ...[
          if (behaviorNote.predefinedTags != null && behaviorNote.predefinedTags!.isNotEmpty)
            const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Text(
              behaviorNote.customNote!,
              style: const TextStyle(
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTag(String tag) {
    final color = _getTypeColor(behaviorNote.type);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        tag,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMetadata() {
    return Row(
      children: [
        if (behaviorNote.subject != null) ...[
          Icon(
            Icons.subject,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            behaviorNote.subject!,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
        if (behaviorNote.subject != null && behaviorNote.isImportant)
          const SizedBox(width: 16),
        if (behaviorNote.isImportant) ...[
          Icon(
            Icons.priority_high,
            size: 16,
            color: AppTheme.warningOrange,
          ),
          const SizedBox(width: 4),
          Text(
            'Important',
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.warningOrange,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ],
    );
  }

  Color _getTypeColor(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return AppTheme.successGreen;
      case BehaviorType.negative:
        return AppTheme.accentRed;
      case BehaviorType.neutral:
        return AppTheme.neutralGray;
    }
  }

  IconData _getTypeIcon(BehaviorType type) {
    switch (type) {
      case BehaviorType.positive:
        return Icons.thumb_up;
      case BehaviorType.negative:
        return Icons.thumb_down;
      case BehaviorType.neutral:
        return Icons.remove;
    }
  }
}
