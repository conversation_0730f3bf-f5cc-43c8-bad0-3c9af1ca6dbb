import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../services/license_service.dart';

class UserManualScreen extends StatefulWidget {
  const UserManualScreen({super.key});

  @override
  State<UserManualScreen> createState() => _UserManualScreenState();
}

class _UserManualScreenState extends State<UserManualScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  final List<ManualSection> _sections = [
    ManualSection(
      title: 'Démarrage',
      icon: Icons.play_arrow,
      content: _getStartedContent(),
    ),
    ManualSection(
      title: 'Élèves',
      icon: Icons.people,
      content: _studentsContent(),
    ),
    ManualSection(title: 'Cours', icon: Icons.book, content: _coursesContent()),
    ManualSection(title: 'Notes', icon: Icons.grade, content: _gradesContent()),
    ManualSection(
      title: 'Présences',
      icon: Icons.event_available,
      content: _attendanceContent(),
    ),
    ManualSection(
      title: 'Comportement',
      icon: Icons.psychology,
      content: _behaviorContent(),
    ),
    ManualSection(
      title: 'Paramètres',
      icon: Icons.settings,
      content: _settingsContent(),
    ),
    ManualSection(
      title: 'Licence',
      icon: Icons.verified,
      content: _licenseContent(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _sections.length, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manuel d\'utilisation'),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _sections.map((section) {
            return Tab(icon: Icon(section.icon), text: section.title);
          }).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _sections.map((section) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader(section),
                const SizedBox(height: 20),
                ...section.content,
              ],
            ),
          );
        }).toList(),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showQuickHelp(),
        icon: const Icon(Icons.help),
        label: const Text('Aide rapide'),
      ),
    );
  }

  Widget _buildSectionHeader(ManualSection section) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(section.icon, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Guide complet pour ${section.title.toLowerCase()}',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showQuickHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help, color: Colors.blue),
            SizedBox(width: 8),
            Text('Aide rapide'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Actions rapides:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Appuyez sur + pour ajouter des éléments'),
              Text('• Glissez pour voir plus d\'options'),
              Text('• Appuyez longuement pour les actions rapides'),
              Text('• Utilisez la recherche en haut des listes'),
              SizedBox(height: 16),
              Text(
                'Navigation:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Tableau de bord: Vue d\'ensemble'),
              Text('• Menu latéral: Accès à toutes les sections'),
              Text('• Paramètres: Configuration de l\'app'),
              SizedBox(height: 16),
              Text(
                'Besoin d\'aide?',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('Contactez le support via les paramètres.'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  static List<Widget> _getStartedContent() {
    return [
      const ManualCard(
        title: 'Bienvenue dans isoucklou',
        content:
            'Cette application vous aide à gérer efficacement votre classe avec des outils modernes et intuitifs.',
        icon: Icons.waving_hand,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Premier démarrage',
        content:
            '1. Configurez vos informations dans Paramètres\n'
            '2. Ajoutez vos élèves\n'
            '3. Créez vos cours et matières\n'
            '4. Commencez à utiliser l\'application!',
        icon: Icons.checklist,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Navigation',
        content:
            'Utilisez le tableau de bord comme point central. '
            'Accédez aux différentes sections via le menu ou les raccourcis.',
        icon: Icons.navigation,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Conseils',
        content:
            '• Explorez les différentes sections\n'
            '• Utilisez les fonctions de recherche\n'
            '• Personnalisez l\'app dans Paramètres\n'
            '• Sauvegardez régulièrement vos données',
        icon: Icons.lightbulb,
        isHighlighted: true,
      ),
    ];
  }

  static List<Widget> _studentsContent() {
    return [
      const ManualCard(
        title: 'Gestion des élèves',
        content:
            'Ajoutez, modifiez et organisez les informations de vos élèves.',
        icon: Icons.people,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Ajouter un élève',
        content:
            '1. Appuyez sur le bouton +\n'
            '2. Remplissez les informations obligatoires\n'
            '3. Ajoutez une photo (optionnel)\n'
            '4. Enregistrez',
        icon: Icons.person_add,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Informations élève',
        content:
            'Nom, prénom, date de naissance, contacts parents, notes personnelles.',
        icon: Icons.info,
      ),
      const SizedBox(height: 16),
      const LimitationCard(
        title: 'Limitation version gratuite',
        content: 'Maximum 10 élèves dans la version gratuite.',
        upgradeFeature: 'unlimited_students',
      ),
    ];
  }

  static List<Widget> _coursesContent() {
    return [
      const ManualCard(
        title: 'Gestion des cours',
        content: 'Organisez vos cours par matières et niveaux.',
        icon: Icons.book,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Créer un cours',
        content:
            '1. Sélectionnez une matière\n'
            '2. Définissez le niveau\n'
            '3. Ajoutez objectifs et description\n'
            '4. Configurez la difficulté',
        icon: Icons.add_box,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Matières',
        content:
            'Gérez vos matières dans la section dédiée. '
            'Associez des couleurs pour une meilleure organisation.',
        icon: Icons.subject,
      ),
      const SizedBox(height: 16),
      const LimitationCard(
        title: 'Limitation version gratuite',
        content: 'Maximum 5 cours dans la version gratuite.',
        upgradeFeature: 'unlimited_courses',
      ),
    ];
  }

  static List<Widget> _gradesContent() {
    return [
      const ManualCard(
        title: 'Système de notation',
        content: 'Utilisez le système français (0-20) pour évaluer vos élèves.',
        icon: Icons.grade,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Ajouter une note',
        content:
            '1. Sélectionnez l\'élève\n'
            '2. Choisissez la matière\n'
            '3. Entrez la note (/20)\n'
            '4. Ajoutez un commentaire',
        icon: Icons.add_circle,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Types d\'évaluation',
        content: 'Contrôle, devoir, oral, projet, participation, etc.',
        icon: Icons.assignment,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Statistiques',
        content: 'Consultez les moyennes, progressions et analyses détaillées.',
        icon: Icons.analytics,
      ),
    ];
  }

  static List<Widget> _attendanceContent() {
    return [
      const ManualCard(
        title: 'Suivi des présences',
        content: 'Enregistrez facilement les présences de vos élèves.',
        icon: Icons.event_available,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Prise de présence',
        content:
            '1. Sélectionnez la date\n'
            '2. Choisissez le cours\n'
            '3. Marquez présent/absent d\'un tap\n'
            '4. Sauvegardez automatiquement',
        icon: Icons.check_circle,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Historique',
        content:
            'Consultez l\'historique des présences par élève ou par période.',
        icon: Icons.history,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Statistiques',
        content: 'Taux de présence, absences répétées, tendances.',
        icon: Icons.bar_chart,
      ),
    ];
  }

  static List<Widget> _behaviorContent() {
    return [
      const ManualCard(
        title: 'Notes de comportement',
        content: 'Suivez le comportement et les incidents de vos élèves.',
        icon: Icons.psychology,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Ajouter une note',
        content:
            '1. Sélectionnez l\'élève\n'
            '2. Choisissez le type (positif/négatif)\n'
            '3. Décrivez l\'incident\n'
            '4. Ajoutez des actions prises',
        icon: Icons.note_add,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Types de comportement',
        content:
            'Participation, perturbation, aide aux autres, travail non fait, etc.',
        icon: Icons.category,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Modèles',
        content: 'Créez des modèles pour les commentaires fréquents.',
        icon: Icons.text_snippet,
      ),
    ];
  }

  static List<Widget> _settingsContent() {
    return [
      const ManualCard(
        title: 'Configuration',
        content: 'Personnalisez l\'application selon vos préférences.',
        icon: Icons.settings,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Informations personnelles',
        content: 'Nom, école, année scolaire - affiché dans les rapports.',
        icon: Icons.person,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Apparence',
        content: 'Mode sombre, taille de police, affichage des photos.',
        icon: Icons.palette,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Notifications',
        content: 'Sons, vibrations, rappels automatiques.',
        icon: Icons.notifications,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Sauvegarde',
        content: 'Export des données, sauvegarde automatique (premium).',
        icon: Icons.backup,
      ),
    ];
  }

  static List<Widget> _licenseContent() {
    return [
      const ManualCard(
        title: 'Version gratuite vs Premium',
        content:
            'Découvrez les fonctionnalités disponibles selon votre licence.',
        icon: Icons.verified,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Version gratuite',
        content:
            '• Maximum 10 élèves\n'
            '• Maximum 5 cours\n'
            '• Fonctionnalités de base\n'
            '• Pas d\'export de données',
        icon: Icons.free_breakfast,
      ),
      const SizedBox(height: 16),
      const ManualCard(
        title: 'Version Premium',
        content:
            '• Élèves et cours illimités\n'
            '• Export de données\n'
            '• Sauvegarde automatique\n'
            '• Rapports avancés\n'
            '• Support prioritaire',
        icon: Icons.star,
        isHighlighted: true,
      ),
      const SizedBox(height: 16),
      const UpgradeCard(),
    ];
  }
}

class ManualSection {
  final String title;
  final IconData icon;
  final List<Widget> content;

  ManualSection({
    required this.title,
    required this.icon,
    required this.content,
  });
}

class ManualCard extends StatelessWidget {
  final String title;
  final String content;
  final IconData icon;
  final bool isHighlighted;

  const ManualCard({
    super.key,
    required this.title,
    required this.content,
    required this.icon,
    this.isHighlighted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isHighlighted
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isHighlighted
              ? Theme.of(context).primaryColor.withOpacity(0.3)
              : Colors.grey.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isHighlighted
                  ? Theme.of(context).primaryColor
                  : Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: isHighlighted ? Colors.white : Colors.grey[600],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isHighlighted
                        ? Theme.of(context).primaryColor
                        : null,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  content,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class LimitationCard extends StatelessWidget {
  final String title;
  final String content;
  final String upgradeFeature;

  const LimitationCard({
    super.key,
    required this.title,
    required this.content,
    required this.upgradeFeature,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        if (appProvider.isLicensed) {
          return const SizedBox.shrink();
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Row(
            children: [
              const Icon(Icons.lock, color: Colors.orange, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      content,
                      style: TextStyle(fontSize: 14, color: Colors.orange[800]),
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () {
                  appProvider.showRestrictionDialog(context, upgradeFeature);
                },
                child: const Text('Débloquer'),
              ),
            ],
          ),
        );
      },
    );
  }
}

class UpgradeCard extends StatelessWidget {
  const UpgradeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        if (appProvider.isLicensed) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: const Row(
              children: [
                Icon(Icons.verified, color: Colors.green, size: 24),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Version Premium activée',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Merci pour votre soutien! 🎉',
                        style: TextStyle(fontSize: 14, color: Colors.green),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.orange[100]!, Colors.orange[50]!],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Column(
            children: [
              const Row(
                children: [
                  Icon(Icons.coffee, color: Colors.orange, size: 24),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Soutenez le développement',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Achetez un café au développeur pour débloquer toutes les fonctionnalités!',
                          style: TextStyle(fontSize: 14, color: Colors.orange),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    LicenseService.showRestrictionDialog(
                      context,
                      'premium_upgrade',
                    );
                  },
                  icon: const Icon(Icons.coffee),
                  label: const Text('Acheter un café ☕'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
