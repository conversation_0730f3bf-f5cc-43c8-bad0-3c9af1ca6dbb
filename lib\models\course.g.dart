// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SubjectAdapter extends TypeAdapter<Subject> {
  @override
  final int typeId = 23;

  @override
  Subject read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Subject(
      id: fields[0] as String?,
      name: fields[1] as String,
      code: fields[2] as String,
      category: fields[3] as CourseCategory,
      description: fields[4] as String?,
      color: fields[5] as String?,
      isActive: fields[6] as bool,
      createdAt: fields[7] as DateTime?,
      updatedAt: fields[8] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Subject obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.code)
      ..writeByte(3)
      ..write(obj.category)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.color)
      ..writeByte(6)
      ..write(obj.isActive)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubjectAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LearningObjectiveAdapter extends TypeAdapter<LearningObjective> {
  @override
  final int typeId = 24;

  @override
  LearningObjective read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LearningObjective(
      id: fields[0] as String?,
      title: fields[1] as String,
      description: fields[2] as String?,
      isCompleted: fields[3] as bool,
      order: fields[4] as int,
    );
  }

  @override
  void write(BinaryWriter writer, LearningObjective obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.isCompleted)
      ..writeByte(4)
      ..write(obj.order);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LearningObjectiveAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CourseAdapter extends TypeAdapter<Course> {
  @override
  final int typeId = 25;

  @override
  Course read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Course(
      id: fields[0] as String?,
      name: fields[1] as String,
      code: fields[2] as String,
      subjectId: fields[3] as String,
      level: fields[4] as EducationLevel,
      description: fields[5] as String?,
      objectives: (fields[6] as List?)?.cast<LearningObjective>(),
      prerequisites: (fields[7] as List?)?.cast<String>(),
      difficulty: fields[8] as CourseDifficulty,
      estimatedHours: fields[9] as int,
      academicYear: fields[10] as String?,
      isActive: fields[11] as bool,
      createdAt: fields[12] as DateTime?,
      updatedAt: fields[13] as DateTime?,
      notes: fields[14] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Course obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.code)
      ..writeByte(3)
      ..write(obj.subjectId)
      ..writeByte(4)
      ..write(obj.level)
      ..writeByte(5)
      ..write(obj.description)
      ..writeByte(6)
      ..write(obj.objectives)
      ..writeByte(7)
      ..write(obj.prerequisites)
      ..writeByte(8)
      ..write(obj.difficulty)
      ..writeByte(9)
      ..write(obj.estimatedHours)
      ..writeByte(10)
      ..write(obj.academicYear)
      ..writeByte(11)
      ..write(obj.isActive)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.updatedAt)
      ..writeByte(14)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CourseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class EducationLevelAdapter extends TypeAdapter<EducationLevel> {
  @override
  final int typeId = 20;

  @override
  EducationLevel read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return EducationLevel.cp;
      case 1:
        return EducationLevel.ce1;
      case 2:
        return EducationLevel.ce2;
      case 3:
        return EducationLevel.cm1;
      case 4:
        return EducationLevel.cm2;
      case 5:
        return EducationLevel.sixieme;
      case 6:
        return EducationLevel.cinquieme;
      case 7:
        return EducationLevel.quatrieme;
      case 8:
        return EducationLevel.troisieme;
      case 9:
        return EducationLevel.seconde;
      case 10:
        return EducationLevel.premiere;
      case 11:
        return EducationLevel.terminale;
      default:
        return EducationLevel.cp;
    }
  }

  @override
  void write(BinaryWriter writer, EducationLevel obj) {
    switch (obj) {
      case EducationLevel.cp:
        writer.writeByte(0);
        break;
      case EducationLevel.ce1:
        writer.writeByte(1);
        break;
      case EducationLevel.ce2:
        writer.writeByte(2);
        break;
      case EducationLevel.cm1:
        writer.writeByte(3);
        break;
      case EducationLevel.cm2:
        writer.writeByte(4);
        break;
      case EducationLevel.sixieme:
        writer.writeByte(5);
        break;
      case EducationLevel.cinquieme:
        writer.writeByte(6);
        break;
      case EducationLevel.quatrieme:
        writer.writeByte(7);
        break;
      case EducationLevel.troisieme:
        writer.writeByte(8);
        break;
      case EducationLevel.seconde:
        writer.writeByte(9);
        break;
      case EducationLevel.premiere:
        writer.writeByte(10);
        break;
      case EducationLevel.terminale:
        writer.writeByte(11);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EducationLevelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CourseCategoryAdapter extends TypeAdapter<CourseCategory> {
  @override
  final int typeId = 21;

  @override
  CourseCategory read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CourseCategory.languesFrancaises;
      case 1:
        return CourseCategory.mathematiques;
      case 2:
        return CourseCategory.sciences;
      case 3:
        return CourseCategory.histoireGeographie;
      case 4:
        return CourseCategory.languesVivantes;
      case 5:
        return CourseCategory.arts;
      case 6:
        return CourseCategory.eps;
      case 7:
        return CourseCategory.technologie;
      case 8:
        return CourseCategory.educationCivique;
      case 9:
        return CourseCategory.philosophie;
      case 10:
        return CourseCategory.specialites;
      case 11:
        return CourseCategory.autre;
      default:
        return CourseCategory.languesFrancaises;
    }
  }

  @override
  void write(BinaryWriter writer, CourseCategory obj) {
    switch (obj) {
      case CourseCategory.languesFrancaises:
        writer.writeByte(0);
        break;
      case CourseCategory.mathematiques:
        writer.writeByte(1);
        break;
      case CourseCategory.sciences:
        writer.writeByte(2);
        break;
      case CourseCategory.histoireGeographie:
        writer.writeByte(3);
        break;
      case CourseCategory.languesVivantes:
        writer.writeByte(4);
        break;
      case CourseCategory.arts:
        writer.writeByte(5);
        break;
      case CourseCategory.eps:
        writer.writeByte(6);
        break;
      case CourseCategory.technologie:
        writer.writeByte(7);
        break;
      case CourseCategory.educationCivique:
        writer.writeByte(8);
        break;
      case CourseCategory.philosophie:
        writer.writeByte(9);
        break;
      case CourseCategory.specialites:
        writer.writeByte(10);
        break;
      case CourseCategory.autre:
        writer.writeByte(11);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CourseCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CourseDifficultyAdapter extends TypeAdapter<CourseDifficulty> {
  @override
  final int typeId = 22;

  @override
  CourseDifficulty read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CourseDifficulty.debutant;
      case 1:
        return CourseDifficulty.intermediaire;
      case 2:
        return CourseDifficulty.avance;
      case 3:
        return CourseDifficulty.expert;
      default:
        return CourseDifficulty.debutant;
    }
  }

  @override
  void write(BinaryWriter writer, CourseDifficulty obj) {
    switch (obj) {
      case CourseDifficulty.debutant:
        writer.writeByte(0);
        break;
      case CourseDifficulty.intermediaire:
        writer.writeByte(1);
        break;
      case CourseDifficulty.avance:
        writer.writeByte(2);
        break;
      case CourseDifficulty.expert:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CourseDifficultyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
