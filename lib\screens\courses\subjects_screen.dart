import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';
import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class SubjectsScreen extends StatefulWidget {
  const SubjectsScreen({super.key});

  @override
  State<SubjectsScreen> createState() => _SubjectsScreenState();
}

class _SubjectsScreenState extends State<SubjectsScreen> {
  String _searchQuery = '';
  CourseCategory? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des Matières'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filtrer',
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          if (appProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              _buildSearchBar(),
              _buildFilterSummary(),
              _buildQuickStats(appProvider),
              Expanded(child: _buildSubjectsList(appProvider)),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, AppRouter.addSubject);
        },
        tooltip: 'Ajouter une matière',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: const InputDecoration(
          hintText: 'Rechercher une matière...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  Widget _buildFilterSummary() {
    if (_selectedCategory == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Wrap(
        spacing: 8.0,
        children: [
          Chip(
            label: Text(_getCourseCategoryName(_selectedCategory!)),
            onDeleted: () {
              setState(() {
                _selectedCategory = null;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(AppProvider appProvider) {
    final filteredSubjects = _getFilteredSubjects(appProvider);
    final totalSubjects = filteredSubjects.length;
    final activeSubjects = filteredSubjects.where((s) => s.isActive).length;

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      totalSubjects.toString(),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: AppTheme.primaryBlue,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Text('Matières total'),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      activeSubjects.toString(),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                            color: AppTheme.successGreen,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Text('Matières actives'),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectsList(AppProvider appProvider) {
    final filteredSubjects = _getFilteredSubjects(appProvider);

    if (filteredSubjects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.subject_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune matière trouvée',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Ajoutez votre première matière ou modifiez vos filtres',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: filteredSubjects.length,
      itemBuilder: (context, index) {
        final subject = filteredSubjects[index];
        final coursesCount = appProvider.getCoursesBySubject(subject.id).length;

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getSubjectColor(subject).withValues(alpha: 0.1),
              child: Icon(
                _getCategoryIcon(subject.category),
                color: _getSubjectColor(subject),
              ),
            ),
            title: Text(
              subject.name,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(subject.code),
                if (subject.description != null)
                  Text(
                    subject.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                Text(
                  '$coursesCount cours • ${_getCourseCategoryName(subject.category)}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  Navigator.pushNamed(
                    context,
                    AppRouter.editSubject,
                    arguments: subject.id,
                  );
                } else if (value == 'delete') {
                  _showDeleteConfirmation(appProvider, subject);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Modifier'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Supprimer', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
            onTap: () {
              Navigator.pushNamed(
                context,
                AppRouter.subjectDetail,
                arguments: subject.id,
              );
            },
          ),
        );
      },
    );
  }

  List<Subject> _getFilteredSubjects(AppProvider appProvider) {
    var subjects = appProvider.activeSubjects;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      subjects = subjects.where((subject) {
        return subject.name.toLowerCase().contains(_searchQuery) ||
            subject.code.toLowerCase().contains(_searchQuery) ||
            (subject.description?.toLowerCase().contains(_searchQuery) ?? false);
      }).toList();
    }

    // Apply category filter
    if (_selectedCategory != null) {
      subjects = subjects.where((subject) => subject.category == _selectedCategory).toList();
    }

    // Sort by name
    subjects.sort((a, b) => a.name.compareTo(b.name));

    return subjects;
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Filtrer les matières'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Catégorie'),
              DropdownButton<CourseCategory?>(
                value: _selectedCategory,
                isExpanded: true,
                items: [
                  const DropdownMenuItem<CourseCategory?>(
                    value: null,
                    child: Text('Toutes les catégories'),
                  ),
                  ...CourseCategory.values.map((category) {
                    return DropdownMenuItem<CourseCategory?>(
                      value: category,
                      child: Text(_getCourseCategoryName(category)),
                    );
                  }),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedCategory = null;
                });
                Navigator.pop(context);
              },
              child: const Text('Réinitialiser'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Fermer'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(AppProvider appProvider, Subject subject) {
    final coursesCount = appProvider.getCoursesBySubject(subject.id).length;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Supprimer la matière'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Êtes-vous sûr de vouloir supprimer "${subject.name}" ?'),
              if (coursesCount > 0) ...[
                const SizedBox(height: 8),
                Text(
                  'Attention: Cette matière est utilisée dans $coursesCount cours.',
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () async {
                try {
                  await appProvider.deleteSubject(subject.id);
                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Matière supprimée avec succès'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Erreur: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Supprimer'),
            ),
          ],
        );
      },
    );
  }

  Color _getSubjectColor(Subject subject) {
    if (subject.color != null) {
      try {
        return Color(int.parse(subject.color!.replaceFirst('#', '0xFF')));
      } catch (e) {
        // Fallback to default color if parsing fails
      }
    }
    return AppTheme.primaryBlue;
  }

  IconData _getCategoryIcon(CourseCategory category) {
    switch (category) {
      case CourseCategory.languesFrancaises:
        return Icons.menu_book;
      case CourseCategory.mathematiques:
        return Icons.calculate;
      case CourseCategory.sciences:
        return Icons.science;
      case CourseCategory.histoireGeographie:
        return Icons.public;
      case CourseCategory.languesVivantes:
        return Icons.language;
      case CourseCategory.arts:
        return Icons.palette;
      case CourseCategory.eps:
        return Icons.sports;
      case CourseCategory.technologie:
        return Icons.computer;
      case CourseCategory.educationCivique:
        return Icons.gavel;
      case CourseCategory.philosophie:
        return Icons.psychology;
      case CourseCategory.specialites:
        return Icons.star;
      case CourseCategory.autre:
        return Icons.subject;
    }
  }

  String _getCourseCategoryName(CourseCategory category) {
    switch (category) {
      case CourseCategory.languesFrancaises:
        return 'Français';
      case CourseCategory.mathematiques:
        return 'Mathématiques';
      case CourseCategory.sciences:
        return 'Sciences';
      case CourseCategory.histoireGeographie:
        return 'Histoire-Géographie';
      case CourseCategory.languesVivantes:
        return 'Langues vivantes';
      case CourseCategory.arts:
        return 'Arts';
      case CourseCategory.eps:
        return 'EPS';
      case CourseCategory.technologie:
        return 'Technologie';
      case CourseCategory.educationCivique:
        return 'Éducation civique';
      case CourseCategory.philosophie:
        return 'Philosophie';
      case CourseCategory.specialites:
        return 'Spécialités';
      case CourseCategory.autre:
        return 'Autre';
    }
  }
}
