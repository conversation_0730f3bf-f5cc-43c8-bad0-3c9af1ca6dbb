import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../providers/app_provider.dart';
import '../../models/lesson.dart';
import '../../utils/app_theme.dart';

class AddLessonScreen extends StatefulWidget {
  final DateTime? initialDate;

  const AddLessonScreen({super.key, this.initialDate});

  @override
  State<AddLessonScreen> createState() => _AddLessonScreenState();
}

class _AddLessonScreenState extends State<AddLessonScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _classroomController = TextEditingController();
  final _contentController = TextEditingController();
  final _homeworkController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedSubject = '';
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _startTime = TimeOfDay.now();
  TimeOfDay _endTime = TimeOfDay.now().replacing(hour: TimeOfDay.now().hour + 1);
  LessonStatus _status = LessonStatus.planned;
  bool _isRecurring = false;
  String _recurringPattern = '';
  final List<String> _objectives = [];
  final List<String> _selectedStudents = [];

  @override
  void initState() {
    super.initState();
    if (widget.initialDate != null) {
      _selectedDate = widget.initialDate!;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _classroomController.dispose();
    _contentController.dispose();
    _homeworkController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouveau Cours'),
        actions: [
          TextButton(
            onPressed: _saveLesson,
            child: const Text('Enregistrer'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBasicInfoSection(),
              const SizedBox(height: 24),
              _buildScheduleSection(),
              const SizedBox(height: 24),
              _buildContentSection(),
              const SizedBox(height: 24),
              _buildStudentsSection(),
              const SizedBox(height: 24),
              _buildRecurrenceSection(),
              const SizedBox(height: 24),
              _buildNotesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations générales',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Titre du cours *',
                isDense: true,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le titre est obligatoire';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Consumer<AppProvider>(
              builder: (context, appProvider, child) {
                final subjects = appProvider.activeSubjects;
                return DropdownButtonFormField<String>(
                  value: _selectedSubject.isEmpty ? null : _selectedSubject,
                  decoration: const InputDecoration(
                    labelText: 'Matière *',
                    isDense: true,
                  ),
                  items: subjects.map((subject) {
                    return DropdownMenuItem(
                      value: subject.name,
                      child: Text(subject.name),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedSubject = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'La matière est obligatoire';
                    }
                    return null;
                  },
                );
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                isDense: true,
                hintText: 'Description du cours...',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _classroomController,
              decoration: const InputDecoration(
                labelText: 'Salle de classe',
                isDense: true,
                hintText: 'Ex: Salle 101, Laboratoire...',
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<LessonStatus>(
              value: _status,
              decoration: const InputDecoration(
                labelText: 'Statut',
                isDense: true,
              ),
              items: LessonStatus.values.map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(_getStatusName(status)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _status = value ?? LessonStatus.planned;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Horaires',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Date',
                        isDense: true,
                      ),
                      child: Text(
                        DateFormat('dd/MM/yyyy').format(_selectedDate),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectStartTime,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Heure de début',
                        isDense: true,
                      ),
                      child: Text(_startTime.format(context)),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectEndTime,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Heure de fin',
                        isDense: true,
                      ),
                      child: Text(_endTime.format(context)),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contenu pédagogique',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _contentController,
              decoration: const InputDecoration(
                labelText: 'Contenu du cours',
                isDense: true,
                hintText: 'Décrivez le contenu du cours...',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _homeworkController,
              decoration: const InputDecoration(
                labelText: 'Devoirs à donner',
                isDense: true,
                hintText: 'Décrivez les devoirs...',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Objectifs pédagogiques',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                TextButton.icon(
                  onPressed: _addObjective,
                  icon: const Icon(Icons.add),
                  label: const Text('Ajouter'),
                ),
              ],
            ),
            if (_objectives.isNotEmpty)
              ...List.generate(_objectives.length, (index) {
                return ListTile(
                  dense: true,
                  title: Text(_objectives[index]),
                  trailing: IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _removeObjective(index),
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Élèves participants',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextButton(
                  onPressed: _selectStudents,
                  child: const Text('Sélectionner'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '${_selectedStudents.length} élève(s) sélectionné(s)',
              style: TextStyle(color: Colors.grey[600]),
            ),
            if (_selectedStudents.isNotEmpty) ...[
              const SizedBox(height: 8),
              Consumer<AppProvider>(
                builder: (context, appProvider, child) {
                  final selectedStudentNames = _selectedStudents
                      .map((id) => appProvider.getStudent(id)?.fullName ?? 'Inconnu')
                      .toList();
                  
                  return Wrap(
                    spacing: 8.0,
                    runSpacing: 4.0,
                    children: selectedStudentNames.map((name) {
                      return Chip(
                        label: Text(name),
                        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                        onDeleted: () {
                          final studentId = _selectedStudents.firstWhere(
                            (id) => appProvider.getStudent(id)?.fullName == name,
                            orElse: () => '',
                          );
                          if (studentId.isNotEmpty) {
                            setState(() {
                              _selectedStudents.remove(studentId);
                            });
                          }
                        },
                      );
                    }).toList(),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecurrenceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Récurrence',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Cours récurrent'),
              subtitle: const Text('Répéter ce cours selon un motif'),
              value: _isRecurring,
              onChanged: (value) {
                setState(() {
                  _isRecurring = value;
                });
              },
            ),
            if (_isRecurring) ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: TextEditingController(text: _recurringPattern),
                decoration: const InputDecoration(
                  labelText: 'Motif de récurrence',
                  isDense: true,
                  hintText: 'Ex: Chaque semaine, Tous les mardis...',
                ),
                onChanged: (value) {
                  _recurringPattern = value;
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes personnelles',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                isDense: true,
                hintText: 'Notes personnelles sur le cours...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  void _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  void _selectStartTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _startTime,
    );
    if (time != null) {
      setState(() {
        _startTime = time;
        // Auto-adjust end time to be 1 hour later
        _endTime = TimeOfDay(
          hour: (time.hour + 1) % 24,
          minute: time.minute,
        );
      });
    }
  }

  void _selectEndTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _endTime,
    );
    if (time != null) {
      setState(() {
        _endTime = time;
      });
    }
  }

  void _addObjective() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('Ajouter un objectif'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'Objectif pédagogique',
              hintText: 'Ex: Comprendre les fractions...',
            ),
            maxLines: 2,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () {
                if (controller.text.trim().isNotEmpty) {
                  setState(() {
                    _objectives.add(controller.text.trim());
                  });
                  Navigator.pop(context);
                }
              },
              child: const Text('Ajouter'),
            ),
          ],
        );
      },
    );
  }

  void _removeObjective(int index) {
    setState(() {
      _objectives.removeAt(index);
    });
  }

void _selectStudents() async {
    final selected = await showDialog<Set<String>>(
      context: context,
      builder: (context) {
        return _StudentSelectionDialog(
          initialSelected: Set<String>.from(_selectedStudents),
        );
      },
    );

    if (selected != null) {
      setState(() {
        _selectedStudents.clear();
        _selectedStudents.addAll(selected);
      });
    }
  }

  void _saveLesson() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final startDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _startTime.hour,
        _startTime.minute,
      );

      final endDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _endTime.hour,
        _endTime.minute,
      );

      if (endDateTime.isBefore(startDateTime)) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('L\'heure de fin doit être après l\'heure de début'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final lesson = Lesson(
        title: _titleController.text.trim(),
        subject: _selectedSubject,
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        startTime: startDateTime,
        endTime: endDateTime,
        status: _status,
        classroom: _classroomController.text.trim().isEmpty
            ? null
            : _classroomController.text.trim(),
        objectives: _objectives.isEmpty ? null : _objectives,
        content: _contentController.text.trim().isEmpty
            ? null
            : _contentController.text.trim(),
        homework: _homeworkController.text.trim().isEmpty
            ? null
            : _homeworkController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        studentIds: _selectedStudents.isEmpty ? null : _selectedStudents,
        isRecurring: _isRecurring,
        recurringPattern: _isRecurring && _recurringPattern.isNotEmpty
            ? _recurringPattern
            : null,
      );

      await context.read<AppProvider>().addLesson(lesson);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Cours "${lesson.title}" créé avec succès'),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de la création: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _getStatusName(LessonStatus status) {
    switch (status) {
      case LessonStatus.planned:
        return 'Planifié';
      case LessonStatus.inProgress:
        return 'En cours';
      case LessonStatus.completed:
        return 'Terminé';
      case LessonStatus.cancelled:
        return 'Annulé';
    }
  }
}

class _StudentSelectionDialog extends StatefulWidget {
  final Set<String> initialSelected;

  const _StudentSelectionDialog({
    required this.initialSelected,
  });

  @override
  _StudentSelectionDialogState createState() => _StudentSelectionDialogState();
}

class _StudentSelectionDialogState extends State<_StudentSelectionDialog> {
  late Set<String> _selectedIds;

  @override
  void initState() {
    super.initState();
    _selectedIds = Set.from(widget.initialSelected);
  }

  @override
  Widget build(BuildContext context) {
    final students = context.read<AppProvider>().activeStudents;

    return AlertDialog(
      title: const Text('Sélectionner des élèves'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: students.map((student) {
            return CheckboxListTile(
              title: Text(student.fullName),
              value: _selectedIds.contains(student.id),
              onChanged: (isSelected) {
                setState(() {
                  if (isSelected == true) {
                    _selectedIds.add(student.id);
                  } else {
                    _selectedIds.remove(student.id);
                  }
                });
              },
            );
          }).toList(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, _selectedIds),
          child: const Text('OK'),
        ),
      ],
    );
  }
}
