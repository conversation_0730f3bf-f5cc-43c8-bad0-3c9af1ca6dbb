import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/course.dart';

import '../../utils/app_theme.dart';
import '../../utils/app_router.dart';

class CourseDetailScreen extends StatefulWidget {
  final String courseId;

  const CourseDetailScreen({super.key, required this.courseId});

  @override
  State<CourseDetailScreen> createState() => _CourseDetailScreenState();
}

class _CourseDetailScreenState extends State<CourseDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Course? _course;
  Subject? _subject;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCourseData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadCourseData() {
    final appProvider = context.read<AppProvider>();
    _course = appProvider.getCourse(widget.courseId);
    if (_course != null) {
      _subject = appProvider.getSubject(_course!.subjectId);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_course == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Cours introuvable')),
        body: const Center(
          child: Text('Ce cours n\'existe pas ou a été supprimé.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_course!.name),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.pushNamed(
                context,
                AppRouter.editCourse,
                arguments: widget.courseId,
              );
            },
            tooltip: 'Modifier le cours',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeleteConfirmation();
              } else if (value == 'duplicate') {
                _duplicateCourse();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('Dupliquer'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Supprimer', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Aperçu', icon: Icon(Icons.info_outline)),
            Tab(text: 'Objectifs', icon: Icon(Icons.flag_outlined)),
            Tab(text: 'Étudiants', icon: Icon(Icons.people_outline)),
            Tab(text: 'Statistiques', icon: Icon(Icons.analytics_outlined)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildObjectivesTab(),
          _buildStudentsTab(),
          _buildStatisticsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course header card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: _getSubjectColor().withValues(
                          alpha: 0.1,
                        ),
                        child: Icon(Icons.school, color: _getSubjectColor()),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _course!.name,
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            Text(
                              _subject?.name ?? 'Matière inconnue',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: Colors.grey[600]),
                            ),
                          ],
                        ),
                      ),
                      Chip(
                        label: Text(_getLevelName(_course!.level)),
                        backgroundColor: _getSubjectColor().withValues(
                          alpha: 0.1,
                        ),
                      ),
                    ],
                  ),
                  if (_course!.description != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Description',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_course!.description!),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Course details
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Détails du cours',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow('Code', _course!.code),
                  _buildDetailRow('Niveau', _getLevelName(_course!.level)),
                  _buildDetailRow(
                    'Difficulté',
                    _getDifficultyName(_course!.difficulty),
                  ),
                  _buildDetailRow(
                    'Heures estimées',
                    '${_course!.estimatedHours}h',
                  ),
                  if (_course!.academicYear != null)
                    _buildDetailRow('Année scolaire', _course!.academicYear!),
                  _buildDetailRow(
                    'Statut',
                    _course!.isActive ? 'Actif' : 'Inactif',
                  ),
                  _buildDetailRow('Créé le', _formatDate(_course!.createdAt)),
                  _buildDetailRow(
                    'Modifié le',
                    _formatDate(_course!.updatedAt),
                  ),
                ],
              ),
            ),
          ),

          if (_course!.notes != null) ...[
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notes',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_course!.notes!),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildObjectivesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Objectifs d\'apprentissage',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          if (_course!.objectives?.isEmpty ?? true)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('Aucun objectif défini pour ce cours.'),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _course!.objectives?.length ?? 0,
              itemBuilder: (context, index) {
                final objective = _course!.objectives![index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getSubjectColor().withValues(
                        alpha: 0.1,
                      ),
                      child: Text('${index + 1}'),
                    ),
                    title: Text(objective.title),
                    subtitle: objective.description != null
                        ? Text(objective.description!)
                        : null,
                    trailing: objective.isCompleted
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(
                            Icons.radio_button_unchecked,
                            color: Colors.grey,
                          ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildStudentsTab() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final students = appProvider.activeStudents;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Étudiants concernés (${students.length})',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),
              if (students.isEmpty)
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text('Aucun étudiant trouvé pour ce niveau.'),
                  ),
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: students.length,
                  itemBuilder: (context, index) {
                    final student = students[index];
                    final grades = appProvider.getStudentGradesBySubject(
                      student.id,
                      _subject?.name ?? '',
                    );
                    final average = grades.isNotEmpty
                        ? grades.fold(
                                0.0,
                                (sum, grade) => sum + grade.normalizedValue,
                              ) /
                              grades.length
                        : null;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          child: Text(
                            student.firstName[0] + student.lastName[0],
                          ),
                        ),
                        title: Text('${student.firstName} ${student.lastName}'),
                        subtitle: Text('${grades.length} notes'),
                        trailing: average != null
                            ? Chip(
                                label: Text('${average.toStringAsFixed(1)}/20'),
                                backgroundColor: _getGradeColor(average),
                              )
                            : const Text('Pas de notes'),
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            AppRouter.studentDetail,
                            arguments: student.id,
                          );
                        },
                      ),
                    );
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatisticsTab() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final students = appProvider.activeStudents;
        final allGrades = students
            .expand(
              (student) => appProvider.getStudentGradesBySubject(
                student.id,
                _subject?.name ?? '',
              ),
            )
            .toList();

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Statistiques du cours',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),
              _buildStatCard('Étudiants inscrits', students.length.toString()),
              _buildStatCard('Notes attribuées', allGrades.length.toString()),
              if (allGrades.isNotEmpty) ...[
                _buildStatCard(
                  'Moyenne générale',
                  '${(allGrades.fold(0.0, (sum, grade) => sum + grade.normalizedValue) / allGrades.length).toStringAsFixed(1)}/20',
                ),
                _buildStatCard(
                  'Note la plus haute',
                  '${allGrades.map((g) => g.normalizedValue).reduce((a, b) => a > b ? a : b).toStringAsFixed(1)}/20',
                ),
                _buildStatCard(
                  'Note la plus basse',
                  '${allGrades.map((g) => g.normalizedValue).reduce((a, b) => a < b ? a : b).toStringAsFixed(1)}/20',
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 2,
              child: Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 1,
              child: Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppTheme.primaryBlue,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSubjectColor() {
    if (_subject?.color != null) {
      return Color(int.parse('0xFF${_subject!.color!.substring(1)}'));
    }
    return AppTheme.primaryBlue;
  }

  String _getLevelName(EducationLevel level) {
    switch (level) {
      case EducationLevel.cp:
        return 'CP';
      case EducationLevel.ce1:
        return 'CE1';
      case EducationLevel.ce2:
        return 'CE2';
      case EducationLevel.cm1:
        return 'CM1';
      case EducationLevel.cm2:
        return 'CM2';
      case EducationLevel.sixieme:
        return '6ème';
      case EducationLevel.cinquieme:
        return '5ème';
      case EducationLevel.quatrieme:
        return '4ème';
      case EducationLevel.troisieme:
        return '3ème';
      case EducationLevel.seconde:
        return '2nde';
      case EducationLevel.premiere:
        return '1ère';
      case EducationLevel.terminale:
        return 'Terminale';
    }
  }

  String _getDifficultyName(CourseDifficulty difficulty) {
    switch (difficulty) {
      case CourseDifficulty.debutant:
        return 'Débutant';
      case CourseDifficulty.intermediaire:
        return 'Intermédiaire';
      case CourseDifficulty.avance:
        return 'Avancé';
      case CourseDifficulty.expert:
        return 'Expert';
    }
  }

  Color _getGradeColor(double grade) {
    if (grade >= 16) return Colors.green.withValues(alpha: 0.1);
    if (grade >= 12) return Colors.orange.withValues(alpha: 0.1);
    return Colors.red.withValues(alpha: 0.1);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le cours'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer le cours "${_course!.name}" ? Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteCourse();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _deleteCourse() async {
    try {
      await context.read<AppProvider>().deleteCourse(widget.courseId);
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cours supprimé avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la suppression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _duplicateCourse() async {
    try {
      await context.read<AppProvider>().duplicateCourse(widget.courseId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cours dupliqué avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la duplication: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
