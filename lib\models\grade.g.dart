// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'grade.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GradeAdapter extends TypeAdapter<Grade> {
  @override
  final int typeId = 8;

  @override
  Grade read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Grade(
      id: fields[0] as String?,
      studentId: fields[1] as String,
      subject: fields[2] as String,
      value: fields[3] as double,
      maxValue: fields[4] as double,
      type: fields[5] as GradeType,
      title: fields[6] as String,
      description: fields[7] as String?,
      date: fields[8] as DateTime,
      createdAt: fields[9] as DateTime?,
      updatedAt: fields[10] as DateTime?,
      coefficient: fields[11] as double,
      feedback: fields[12] as String?,
      isPublished: fields[13] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Grade obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.value)
      ..writeByte(4)
      ..write(obj.maxValue)
      ..writeByte(5)
      ..write(obj.type)
      ..writeByte(6)
      ..write(obj.title)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.date)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.updatedAt)
      ..writeByte(11)
      ..write(obj.coefficient)
      ..writeByte(12)
      ..write(obj.feedback)
      ..writeByte(13)
      ..write(obj.isPublished);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GradeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SubjectAverageAdapter extends TypeAdapter<SubjectAverage> {
  @override
  final int typeId = 9;

  @override
  SubjectAverage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SubjectAverage(
      id: fields[0] as String?,
      studentId: fields[1] as String,
      subject: fields[2] as String,
      average: fields[3] as double,
      gradeCount: fields[4] as int,
      calculatedAt: fields[5] as DateTime?,
      periodStart: fields[6] as DateTime,
      periodEnd: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SubjectAverage obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.average)
      ..writeByte(4)
      ..write(obj.gradeCount)
      ..writeByte(5)
      ..write(obj.calculatedAt)
      ..writeByte(6)
      ..write(obj.periodStart)
      ..writeByte(7)
      ..write(obj.periodEnd);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubjectAverageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class GradeTypeAdapter extends TypeAdapter<GradeType> {
  @override
  final int typeId = 7;

  @override
  GradeType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return GradeType.evaluation;
      case 1:
        return GradeType.homework;
      case 2:
        return GradeType.participation;
      case 3:
        return GradeType.project;
      case 4:
        return GradeType.exam;
      case 5:
        return GradeType.quiz;
      default:
        return GradeType.evaluation;
    }
  }

  @override
  void write(BinaryWriter writer, GradeType obj) {
    switch (obj) {
      case GradeType.evaluation:
        writer.writeByte(0);
        break;
      case GradeType.homework:
        writer.writeByte(1);
        break;
      case GradeType.participation:
        writer.writeByte(2);
        break;
      case GradeType.project:
        writer.writeByte(3);
        break;
      case GradeType.exam:
        writer.writeByte(4);
        break;
      case GradeType.quiz:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GradeTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
