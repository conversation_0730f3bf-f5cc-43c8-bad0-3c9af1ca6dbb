// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'behavior_note.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BehaviorNoteAdapter extends TypeAdapter<BehaviorNote> {
  @override
  final int typeId = 6;

  @override
  BehaviorNote read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BehaviorNote(
      id: fields[0] as String?,
      studentId: fields[1] as String,
      date: fields[2] as DateTime,
      type: fields[3] as BehaviorType,
      predefinedTags: (fields[4] as List?)?.cast<PredefinedBehaviorTag>(),
      customNote: fields[5] as String?,
      subject: fields[6] as String?,
      createdAt: fields[7] as DateTime?,
      updatedAt: fields[8] as DateTime?,
      isImportant: fields[9] as bool,
      lessonId: fields[10] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, BehaviorNote obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.date)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.predefinedTags)
      ..writeByte(5)
      ..write(obj.customNote)
      ..writeByte(6)
      ..write(obj.subject)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.isImportant)
      ..writeByte(10)
      ..write(obj.lessonId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BehaviorNoteAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BehaviorTypeAdapter extends TypeAdapter<BehaviorType> {
  @override
  final int typeId = 4;

  @override
  BehaviorType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return BehaviorType.positive;
      case 1:
        return BehaviorType.negative;
      case 2:
        return BehaviorType.neutral;
      default:
        return BehaviorType.positive;
    }
  }

  @override
  void write(BinaryWriter writer, BehaviorType obj) {
    switch (obj) {
      case BehaviorType.positive:
        writer.writeByte(0);
        break;
      case BehaviorType.negative:
        writer.writeByte(1);
        break;
      case BehaviorType.neutral:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BehaviorTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PredefinedBehaviorTagAdapter extends TypeAdapter<PredefinedBehaviorTag> {
  @override
  final int typeId = 5;

  @override
  PredefinedBehaviorTag read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PredefinedBehaviorTag.late;
      case 1:
        return PredefinedBehaviorTag.participated;
      case 2:
        return PredefinedBehaviorTag.homeworkMissing;
      case 3:
        return PredefinedBehaviorTag.homeworkComplete;
      case 4:
        return PredefinedBehaviorTag.disruptive;
      case 5:
        return PredefinedBehaviorTag.helpful;
      case 6:
        return PredefinedBehaviorTag.excellent;
      case 7:
        return PredefinedBehaviorTag.needsImprovement;
      case 8:
        return PredefinedBehaviorTag.absent;
      case 9:
        return PredefinedBehaviorTag.sick;
      case 10:
        return PredefinedBehaviorTag.goodEffort;
      case 11:
        return PredefinedBehaviorTag.lackOfEffort;
      case 12:
        return PredefinedBehaviorTag.creative;
      case 13:
        return PredefinedBehaviorTag.leadership;
      case 14:
        return PredefinedBehaviorTag.teamwork;
      default:
        return PredefinedBehaviorTag.late;
    }
  }

  @override
  void write(BinaryWriter writer, PredefinedBehaviorTag obj) {
    switch (obj) {
      case PredefinedBehaviorTag.late:
        writer.writeByte(0);
        break;
      case PredefinedBehaviorTag.participated:
        writer.writeByte(1);
        break;
      case PredefinedBehaviorTag.homeworkMissing:
        writer.writeByte(2);
        break;
      case PredefinedBehaviorTag.homeworkComplete:
        writer.writeByte(3);
        break;
      case PredefinedBehaviorTag.disruptive:
        writer.writeByte(4);
        break;
      case PredefinedBehaviorTag.helpful:
        writer.writeByte(5);
        break;
      case PredefinedBehaviorTag.excellent:
        writer.writeByte(6);
        break;
      case PredefinedBehaviorTag.needsImprovement:
        writer.writeByte(7);
        break;
      case PredefinedBehaviorTag.absent:
        writer.writeByte(8);
        break;
      case PredefinedBehaviorTag.sick:
        writer.writeByte(9);
        break;
      case PredefinedBehaviorTag.goodEffort:
        writer.writeByte(10);
        break;
      case PredefinedBehaviorTag.lackOfEffort:
        writer.writeByte(11);
        break;
      case PredefinedBehaviorTag.creative:
        writer.writeByte(12);
        break;
      case PredefinedBehaviorTag.leadership:
        writer.writeByte(13);
        break;
      case PredefinedBehaviorTag.teamwork:
        writer.writeByte(14);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PredefinedBehaviorTagAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
