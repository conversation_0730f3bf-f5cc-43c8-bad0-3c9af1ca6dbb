// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LessonAttachmentAdapter extends TypeAdapter<LessonAttachment> {
  @override
  final int typeId = 14;

  @override
  LessonAttachment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LessonAttachment(
      id: fields[0] as String?,
      name: fields[1] as String,
      path: fields[2] as String,
      type: fields[3] as String,
      description: fields[4] as String?,
      addedAt: fields[5] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, LessonAttachment obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.path)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.addedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LessonAttachmentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LessonAdapter extends TypeAdapter<Lesson> {
  @override
  final int typeId = 15;

  @override
  Lesson read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Lesson(
      id: fields[0] as String?,
      title: fields[1] as String,
      subject: fields[2] as String,
      description: fields[3] as String?,
      startTime: fields[4] as DateTime,
      endTime: fields[5] as DateTime,
      status: fields[6] as LessonStatus,
      classroom: fields[7] as String?,
      objectives: (fields[8] as List?)?.cast<String>(),
      content: fields[9] as String?,
      homework: fields[10] as String?,
      attachments: (fields[11] as List?)?.cast<LessonAttachment>(),
      notes: fields[12] as String?,
      createdAt: fields[13] as DateTime?,
      updatedAt: fields[14] as DateTime?,
      studentIds: (fields[15] as List?)?.cast<String>(),
      isRecurring: fields[16] as bool,
      recurringPattern: fields[17] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Lesson obj) {
    writer
      ..writeByte(18)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.startTime)
      ..writeByte(5)
      ..write(obj.endTime)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.classroom)
      ..writeByte(8)
      ..write(obj.objectives)
      ..writeByte(9)
      ..write(obj.content)
      ..writeByte(10)
      ..write(obj.homework)
      ..writeByte(11)
      ..write(obj.attachments)
      ..writeByte(12)
      ..write(obj.notes)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt)
      ..writeByte(15)
      ..write(obj.studentIds)
      ..writeByte(16)
      ..write(obj.isRecurring)
      ..writeByte(17)
      ..write(obj.recurringPattern);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LessonAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LessonStatusAdapter extends TypeAdapter<LessonStatus> {
  @override
  final int typeId = 13;

  @override
  LessonStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return LessonStatus.planned;
      case 1:
        return LessonStatus.inProgress;
      case 2:
        return LessonStatus.completed;
      case 3:
        return LessonStatus.cancelled;
      default:
        return LessonStatus.planned;
    }
  }

  @override
  void write(BinaryWriter writer, LessonStatus obj) {
    switch (obj) {
      case LessonStatus.planned:
        writer.writeByte(0);
        break;
      case LessonStatus.inProgress:
        writer.writeByte(1);
        break;
      case LessonStatus.completed:
        writer.writeByte(2);
        break;
      case LessonStatus.cancelled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LessonStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
