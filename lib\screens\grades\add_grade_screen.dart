import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/app_provider.dart';
import '../../models/grade.dart';
import '../../utils/app_theme.dart';

class AddGradeScreen extends StatefulWidget {
  final String? studentId;
  final String? subject;
  final String? gradeId;

  const AddGradeScreen({super.key, this.studentId, this.subject, this.gradeId});

  @override
  State<AddGradeScreen> createState() => _AddGradeScreenState();
}

class _AddGradeScreenState extends State<AddGradeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _valueController = TextEditingController();
  final _maxValueController = TextEditingController();
  final _coefficientController = TextEditingController();
  final _feedbackController = TextEditingController();
  final _subjectController = TextEditingController();

  String? _selectedStudentId;
  GradeType _selectedType = GradeType.evaluation;
  DateTime _selectedDate = DateTime.now();
  bool _isPublished = false;
  bool _isLoading = false;
  Grade? _existingGrade;

  // Common French subjects
  final List<String> _commonSubjects = [
    'Français',
    'Mathématiques',
    'Histoire-Géographie',
    'Sciences et Vie de la Terre',
    'Physique-Chimie',
    'Anglais',
    'Espagnol',
    'Allemand',
    'Arts Plastiques',
    'Éducation Musicale',
    'Éducation Physique et Sportive',
    'Technologie',
    'Éducation Civique',
  ];

  @override
  void initState() {
    super.initState();
    _selectedStudentId = widget.studentId;
    _subjectController.text = widget.subject ?? '';
    _maxValueController.text = '20';
    _coefficientController.text = '1';
    _loadExistingGrade();
  }

  void _loadExistingGrade() {
    if (widget.gradeId != null) {
      final appProvider = context.read<AppProvider>();
      _existingGrade = appProvider.grades
          .where((grade) => grade.id == widget.gradeId)
          .firstOrNull;

      if (_existingGrade != null) {
        _selectedStudentId = _existingGrade!.studentId;
        _titleController.text = _existingGrade!.title;
        _descriptionController.text = _existingGrade!.description ?? '';
        _valueController.text = _existingGrade!.value.toString();
        _maxValueController.text = _existingGrade!.maxValue.toString();
        _coefficientController.text = _existingGrade!.coefficient.toString();
        _feedbackController.text = _existingGrade!.feedback ?? '';
        _subjectController.text = _existingGrade!.subject;
        _selectedType = _existingGrade!.type;
        _selectedDate = _existingGrade!.date;
        _isPublished = _existingGrade!.isPublished;
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _valueController.dispose();
    _maxValueController.dispose();
    _coefficientController.dispose();
    _feedbackController.dispose();
    _subjectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = _existingGrade != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Modifier la note' : 'Ajouter une note'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveGrade,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Enregistrer'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildStudentAndSubjectSelection(),
            const SizedBox(height: 24),
            _buildGradeDetails(),
            const SizedBox(height: 24),
            _buildGradeValue(),
            const SizedBox(height: 24),
            _buildAdditionalInfo(),
            const SizedBox(height: 24),
            _buildPublishingOptions(),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentAndSubjectSelection() {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final students = appProvider.activeStudents;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Élève et matière',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedStudentId,
                  isExpanded: true,
                  decoration: const InputDecoration(
                    labelText: 'Sélectionner un élève *',
                    prefixIcon: Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez sélectionner un élève';
                    }
                    return null;
                  },
                  items: students.map((student) {
                    return DropdownMenuItem(
                      value: student.id,
                      child: Text(
                        student.fullName,
                        overflow: TextOverflow.ellipsis,
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedStudentId = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _commonSubjects.contains(_subjectController.text)
                      ? _subjectController.text
                      : null,
                  isExpanded: true,
                  decoration: const InputDecoration(
                    labelText: 'Matière *',
                    prefixIcon: Icon(Icons.subject),
                  ),
                  items: _commonSubjects.map((subject) {
                    return DropdownMenuItem(
                      value: subject,
                      child: Text(subject, overflow: TextOverflow.ellipsis),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      _subjectController.text = value;
                    }
                  },
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _subjectController,
                  decoration: const InputDecoration(
                    labelText: 'Ou saisir une matière personnalisée',
                    prefixIcon: Icon(Icons.edit),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Veuillez sélectionner ou saisir une matière';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGradeDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Détails de l\'évaluation',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Titre de l\'évaluation *',
                hintText: 'Ex: Contrôle chapitre 3, Exposé oral...',
                prefixIcon: Icon(Icons.title),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Veuillez saisir un titre';
                }
                return null;
              },
              textCapitalization: TextCapitalization.sentences,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<GradeType>(
              value: _selectedType,
              isExpanded: true,
              decoration: const InputDecoration(
                labelText: 'Type d\'évaluation',
                prefixIcon: Icon(Icons.category),
              ),
              items: GradeType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Row(
                    children: [
                      Icon(_getTypeIcon(type), size: 20),
                      const SizedBox(width: 8),
                      Text(_getTypeDisplayName(type)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: _selectDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Date de l\'évaluation',
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optionnel)',
                hintText: 'Détails sur l\'évaluation...',
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGradeValue() {
    final currentValue = double.tryParse(_valueController.text) ?? 0.0;
    final maxValue = double.tryParse(_maxValueController.text) ?? 20.0;
    final normalizedValue = maxValue > 0
        ? (currentValue / maxValue) * 20.0
        : 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Note',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: _valueController,
                    decoration: const InputDecoration(
                      labelText: 'Note obtenue *',
                      prefixIcon: Icon(Icons.grade),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Veuillez saisir une note';
                      }
                      final grade = double.tryParse(value);
                      if (grade == null) {
                        return 'Note invalide';
                      }
                      final max =
                          double.tryParse(_maxValueController.text) ?? 20;
                      if (grade < 0 || grade > max) {
                        return 'Note entre 0 et $max';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {}); // Refresh to update preview
                    },
                  ),
                ),
                const SizedBox(width: 16),
                const Text('/', style: TextStyle(fontSize: 24)),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _maxValueController,
                    decoration: const InputDecoration(labelText: 'Note max'),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    validator: (value) {
                      final max = double.tryParse(value ?? '');
                      if (max == null || max <= 0) {
                        return 'Note max invalide';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {}); // Refresh to update preview
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _coefficientController,
              decoration: const InputDecoration(
                labelText: 'Coefficient',
                hintText: '1.0 par défaut',
                prefixIcon: Icon(Icons.functions),
              ),
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
              validator: (value) {
                final coeff = double.tryParse(value ?? '1');
                if (coeff == null || coeff <= 0) {
                  return 'Coefficient invalide';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            // Grade preview
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.getGradeColor(
                  normalizedValue,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.getGradeColor(normalizedValue),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    'Aperçu de la note',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getGradeColor(normalizedValue),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    maxValue == 20
                        ? '${currentValue.toStringAsFixed(1)}/20'
                        : '${currentValue.toStringAsFixed(1)}/$maxValue (${normalizedValue.toStringAsFixed(1)}/20)',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.getGradeColor(normalizedValue),
                    ),
                  ),
                  Text(
                    _getAppreciation(normalizedValue),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.getGradeColor(normalizedValue),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Commentaire',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _feedbackController,
              decoration: const InputDecoration(
                labelText: 'Commentaire du professeur',
                hintText: 'Félicitations ! Bon travail ! À revoir...',
                prefixIcon: Icon(Icons.feedback),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPublishingOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Publication',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SwitchListTile(
              title: const Text('Publier la note'),
              subtitle: const Text(
                'La note sera visible par les élèves et parents',
              ),
              value: _isPublished,
              onChanged: (value) {
                setState(() {
                  _isPublished = value;
                });
              },
              activeColor: AppTheme.successGreen,
            ),
          ],
        ),
      ),
    );
  }

  String _getTypeDisplayName(GradeType type) {
    switch (type) {
      case GradeType.evaluation:
        return 'Évaluation';
      case GradeType.homework:
        return 'Devoir';
      case GradeType.participation:
        return 'Participation';
      case GradeType.project:
        return 'Projet';
      case GradeType.exam:
        return 'Examen';
      case GradeType.quiz:
        return 'Interrogation';
    }
  }

  IconData _getTypeIcon(GradeType type) {
    switch (type) {
      case GradeType.evaluation:
        return Icons.assignment;
      case GradeType.homework:
        return Icons.home_work;
      case GradeType.participation:
        return Icons.record_voice_over;
      case GradeType.project:
        return Icons.folder_special;
      case GradeType.exam:
        return Icons.quiz;
      case GradeType.quiz:
        return Icons.help_outline;
    }
  }

  String _getAppreciation(double grade) {
    if (grade >= 18) return 'Excellent';
    if (grade >= 16) return 'Très bien';
    if (grade >= 14) return 'Bien';
    if (grade >= 12) return 'Assez bien';
    if (grade >= 10) return 'Passable';
    if (grade >= 8) return 'Insuffisant';
    return 'Très insuffisant';
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _saveGrade() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final grade = Grade(
        id: _existingGrade?.id,
        studentId: _selectedStudentId!,
        subject: _subjectController.text.trim(),
        value: double.tryParse(_valueController.text) ?? 0.0,
        maxValue: double.tryParse(_maxValueController.text) ?? 20.0,
        type: _selectedType,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        date: _selectedDate,
        coefficient: double.tryParse(_coefficientController.text) ?? 1.0,
        feedback: _feedbackController.text.trim().isEmpty
            ? null
            : _feedbackController.text.trim(),
        isPublished: _isPublished,
        createdAt: _existingGrade?.createdAt,
      );

      final appProvider = context.read<AppProvider>();
      if (_existingGrade != null) {
        await appProvider.updateGrade(grade);
      } else {
        await appProvider.addGrade(grade);
      }

      if (mounted) {
        final student = appProvider.getStudent(_selectedStudentId!);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _existingGrade != null
                  ? 'Note modifiée pour ${student?.fullName}'
                  : 'Note ajoutée pour ${student?.fullName}',
            ),
            backgroundColor: AppTheme.successGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'enregistrement: $e'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
