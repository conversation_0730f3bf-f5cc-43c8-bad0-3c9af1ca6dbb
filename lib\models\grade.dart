import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'grade.g.dart';

@HiveType(typeId: 7)
enum GradeType {
  @HiveField(0)
  evaluation, // Évaluation
  @HiveField(1)
  homework, // Devoir
  @HiveField(2)
  participation, // Participation
  @HiveField(3)
  project, // Projet
  @HiveField(4)
  exam, // Examen
  @HiveField(5)
  quiz, // Interrogation
}

@HiveType(typeId: 8)
class Grade extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String studentId;

  @HiveField(2)
  late String subject; // Matière

  @HiveField(3)
  late double value; // Note sur 20

  @HiveField(4)
  late double maxValue; // Note maximale (généralement 20)

  @HiveField(5)
  late GradeType type;

  @HiveField(6)
  late String title; // Titre de l'évaluation

  @HiveField(7)
  String? description;

  @HiveField(8)
  late DateTime date;

  @HiveField(9)
  late DateTime createdAt;

  @HiveField(10)
  late DateTime updatedAt;

  @HiveField(11)
  double coefficient; // Coefficient pour le calcul de moyenne

  @HiveField(12)
  String? feedback; // Commentaire du professeur

  @HiveField(13)
  bool isPublished; // Si la note est publiée aux élèves/parents

  Grade({
    String? id,
    required this.studentId,
    required this.subject,
    required this.value,
    this.maxValue = 20.0,
    required this.type,
    required this.title,
    this.description,
    required this.date,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.coefficient = 1.0,
    this.feedback,
    this.isPublished = false,
  }) {
    this.id = id ?? const Uuid().v4();
    this.createdAt = createdAt ?? DateTime.now();
    this.updatedAt = updatedAt ?? DateTime.now();
  }

  void updateTimestamp() {
    updatedAt = DateTime.now();
  }

  // Convert grade to scale of 20 if different max value
  double get normalizedValue => (value / maxValue) * 20.0;

  // Get grade as percentage
  double get percentage => (value / maxValue) * 100.0;

  // French grading appreciation
  String get appreciation {
    final normalized = normalizedValue;
    if (normalized >= 18) return 'Excellent';
    if (normalized >= 16) return 'Très bien';
    if (normalized >= 14) return 'Bien';
    if (normalized >= 12) return 'Assez bien';
    if (normalized >= 10) return 'Passable';
    if (normalized >= 8) return 'Insuffisant';
    return 'Très insuffisant';
  }

  String get typeDisplayName {
    switch (type) {
      case GradeType.evaluation:
        return 'Évaluation';
      case GradeType.homework:
        return 'Devoir';
      case GradeType.participation:
        return 'Participation';
      case GradeType.project:
        return 'Projet';
      case GradeType.exam:
        return 'Examen';
      case GradeType.quiz:
        return 'Interrogation';
    }
  }

  String get displayValue {
    if (maxValue == 20.0) {
      return '${value.toStringAsFixed(1)}/20';
    } else {
      return '${value.toStringAsFixed(1)}/${maxValue.toStringAsFixed(1)} (${normalizedValue.toStringAsFixed(1)}/20)';
    }
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'studentId': studentId,
    'subject': subject,
    'value': value,
    'maxValue': maxValue,
    'type': type.index,
    'title': title,
    'description': description,
    'date': date.toIso8601String(),
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'coefficient': coefficient,
    'feedback': feedback,
    'isPublished': isPublished,
  };

  factory Grade.fromJson(Map<String, dynamic> json) => Grade(
    id: json['id'],
    studentId: json['studentId'],
    subject: json['subject'],
    value: json['value'].toDouble(),
    maxValue: json['maxValue']?.toDouble() ?? 20.0,
    type: GradeType.values[json['type']],
    title: json['title'],
    description: json['description'],
    date: DateTime.parse(json['date']),
    createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    coefficient: json['coefficient']?.toDouble() ?? 1.0,
    feedback: json['feedback'],
    isPublished: json['isPublished'] ?? false,
  );
}

@HiveType(typeId: 9)
class SubjectAverage extends HiveObject {
  @HiveField(0)
  late String id;

  @HiveField(1)
  late String studentId;

  @HiveField(2)
  late String subject;

  @HiveField(3)
  late double average; // Moyenne sur 20

  @HiveField(4)
  late int gradeCount; // Nombre de notes

  @HiveField(5)
  late DateTime calculatedAt;

  @HiveField(6)
  late DateTime periodStart;

  @HiveField(7)
  late DateTime periodEnd;

  SubjectAverage({
    String? id,
    required this.studentId,
    required this.subject,
    required this.average,
    required this.gradeCount,
    DateTime? calculatedAt,
    required this.periodStart,
    required this.periodEnd,
  }) {
    this.id = id ?? const Uuid().v4();
    this.calculatedAt = calculatedAt ?? DateTime.now();
  }

  String get appreciation {
    if (average >= 18) return 'Excellent';
    if (average >= 16) return 'Très bien';
    if (average >= 14) return 'Bien';
    if (average >= 12) return 'Assez bien';
    if (average >= 10) return 'Passable';
    if (average >= 8) return 'Insuffisant';
    return 'Très insuffisant';
  }

  String get displayAverage => '${average.toStringAsFixed(2)}/20';

  Map<String, dynamic> toJson() => {
    'id': id,
    'studentId': studentId,
    'subject': subject,
    'average': average,
    'gradeCount': gradeCount,
    'calculatedAt': calculatedAt.toIso8601String(),
    'periodStart': periodStart.toIso8601String(),
    'periodEnd': periodEnd.toIso8601String(),
  };

  factory SubjectAverage.fromJson(Map<String, dynamic> json) => SubjectAverage(
    id: json['id'],
    studentId: json['studentId'],
    subject: json['subject'],
    average: json['average'].toDouble(),
    gradeCount: json['gradeCount'],
    calculatedAt: json['calculatedAt'] != null ? DateTime.parse(json['calculatedAt']) : null,
    periodStart: DateTime.parse(json['periodStart']),
    periodEnd: DateTime.parse(json['periodEnd']),
  );
}
