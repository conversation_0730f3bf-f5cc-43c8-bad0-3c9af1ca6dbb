import 'package:hive_flutter/hive_flutter.dart';
import '../models/student.dart';
import '../models/attendance.dart';
import '../models/behavior_note.dart';
import '../models/grade.dart';
import '../models/rubric.dart';
import '../models/lesson.dart';
import '../models/feedback_template.dart';
import '../models/course.dart';

class DatabaseService {
  static const String studentsBoxName = 'students';
  static const String attendanceBoxName = 'attendance';
  static const String dailyAttendanceBoxName = 'daily_attendance';
  static const String behaviorNotesBoxName = 'behavior_notes';
  static const String gradesBoxName = 'grades';
  static const String subjectAveragesBoxName = 'subject_averages';
  static const String rubricsBoxName = 'rubrics';
  static const String rubricAssessmentsBoxName = 'rubric_assessments';
  static const String lessonsBoxName = 'lessons';
  static const String feedbackTemplatesBoxName = 'feedback_templates';
  static const String subjectsBoxName = 'subjects';
  static const String coursesBoxName = 'courses';

  static late Box<Student> _studentsBox;
  static late Box<AttendanceRecord> _attendanceBox;
  static late Box<DailyAttendance> _dailyAttendanceBox;
  static late Box<BehaviorNote> _behaviorNotesBox;
  static late Box<Grade> _gradesBox;
  static late Box<SubjectAverage> _subjectAveragesBox;
  static late Box<Rubric> _rubricsBox;
  static late Box<RubricAssessment> _rubricAssessmentsBox;
  static late Box<Lesson> _lessonsBox;
  static late Box<FeedbackTemplate> _feedbackTemplatesBox;
  static late Box<Subject> _subjectsBox;
  static late Box<Course> _coursesBox;

  static Future<void> init() async {
    // Initialize Hive
    await Hive.initFlutter();

    // Register adapters
    Hive.registerAdapter(StudentAdapter());
    Hive.registerAdapter(AttendanceStatusAdapter());
    Hive.registerAdapter(AttendanceRecordAdapter());
    Hive.registerAdapter(DailyAttendanceAdapter());
    Hive.registerAdapter(BehaviorTypeAdapter());
    Hive.registerAdapter(PredefinedBehaviorTagAdapter());
    Hive.registerAdapter(BehaviorNoteAdapter());
    Hive.registerAdapter(GradeTypeAdapter());
    Hive.registerAdapter(GradeAdapter());
    Hive.registerAdapter(SubjectAverageAdapter());
    Hive.registerAdapter(RubricCriterionAdapter());
    Hive.registerAdapter(RubricAdapter());
    Hive.registerAdapter(RubricAssessmentAdapter());
    Hive.registerAdapter(LessonStatusAdapter());
    Hive.registerAdapter(LessonAttachmentAdapter());
    Hive.registerAdapter(LessonAdapter());
    Hive.registerAdapter(FeedbackCategoryAdapter());
    Hive.registerAdapter(FeedbackTemplateAdapter());
    // Course management adapters
    Hive.registerAdapter(EducationLevelAdapter());
    Hive.registerAdapter(CourseCategoryAdapter());
    Hive.registerAdapter(CourseDifficultyAdapter());
    Hive.registerAdapter(SubjectAdapter());
    Hive.registerAdapter(LearningObjectiveAdapter());
    Hive.registerAdapter(CourseAdapter());

    // Open boxes
    _studentsBox = await Hive.openBox<Student>(studentsBoxName);
    _attendanceBox = await Hive.openBox<AttendanceRecord>(attendanceBoxName);
    _dailyAttendanceBox = await Hive.openBox<DailyAttendance>(
      dailyAttendanceBoxName,
    );
    _behaviorNotesBox = await Hive.openBox<BehaviorNote>(behaviorNotesBoxName);
    _gradesBox = await Hive.openBox<Grade>(gradesBoxName);
    _subjectAveragesBox = await Hive.openBox<SubjectAverage>(
      subjectAveragesBoxName,
    );
    _rubricsBox = await Hive.openBox<Rubric>(rubricsBoxName);
    _rubricAssessmentsBox = await Hive.openBox<RubricAssessment>(
      rubricAssessmentsBoxName,
    );
    _lessonsBox = await Hive.openBox<Lesson>(lessonsBoxName);
    _feedbackTemplatesBox = await Hive.openBox<FeedbackTemplate>(
      feedbackTemplatesBoxName,
    );
    _subjectsBox = await Hive.openBox<Subject>(subjectsBoxName);
    _coursesBox = await Hive.openBox<Course>(coursesBoxName);

    // Initialize default data if empty
    await _initializeDefaultFeedbackTemplates();
    await _initializeDefaultSubjects();
  }

  static Future<void> _initializeDefaultFeedbackTemplates() async {
    if (_feedbackTemplatesBox.isEmpty) {
      final defaultTemplates = FeedbackTemplate.getDefaultTemplates();
      for (final template in defaultTemplates) {
        await _feedbackTemplatesBox.put(template.id, template);
      }
    }
  }

  static Future<void> _initializeDefaultSubjects() async {
    if (_subjectsBox.isEmpty) {
      final defaultSubjects = [
        Subject(
          name: 'Français',
          code: 'FR',
          category: CourseCategory.languesFrancaises,
          description: 'Langue française, littérature et expression écrite',
          color: '#E3F2FD',
        ),
        Subject(
          name: 'Mathématiques',
          code: 'MATH',
          category: CourseCategory.mathematiques,
          description: 'Mathématiques et logique',
          color: '#F3E5F5',
        ),
        Subject(
          name: 'Histoire-Géographie',
          code: 'HG',
          category: CourseCategory.histoireGeographie,
          description: 'Histoire et géographie',
          color: '#FFF3E0',
        ),
        Subject(
          name: 'Sciences et Vie de la Terre',
          code: 'SVT',
          category: CourseCategory.sciences,
          description: 'Sciences de la vie et de la terre',
          color: '#E8F5E8',
        ),
        Subject(
          name: 'Physique-Chimie',
          code: 'PC',
          category: CourseCategory.sciences,
          description: 'Physique et chimie',
          color: '#E0F2F1',
        ),
        Subject(
          name: 'Anglais',
          code: 'ANG',
          category: CourseCategory.languesVivantes,
          description: 'Langue anglaise',
          color: '#FFF8E1',
        ),
        Subject(
          name: 'Éducation Physique et Sportive',
          code: 'EPS',
          category: CourseCategory.eps,
          description: 'Éducation physique et sportive',
          color: '#FFEBEE',
        ),
        Subject(
          name: 'Arts Plastiques',
          code: 'AP',
          category: CourseCategory.arts,
          description: 'Arts plastiques et visuels',
          color: '#F1F8E9',
        ),
      ];

      for (final subject in defaultSubjects) {
        await _subjectsBox.put(subject.id, subject);
      }
    }
  }

  // Student operations
  static Box<Student> get studentsBox => _studentsBox;
  static List<Student> get allStudents =>
      _studentsBox.values.where((s) => s.isActive).toList();

  static Future<void> saveStudent(Student student) async {
    student.updateTimestamp();
    await _studentsBox.put(student.id, student);
  }

  static Future<void> deleteStudent(String studentId) async {
    final student = _studentsBox.get(studentId);
    if (student != null) {
      student.isActive = false;
      student.updateTimestamp();
      await _studentsBox.put(studentId, student);
    }
  }

  static Student? getStudent(String studentId) => _studentsBox.get(studentId);

  // Attendance operations
  static Box<AttendanceRecord> get attendanceBox => _attendanceBox;
  static Box<DailyAttendance> get dailyAttendanceBox => _dailyAttendanceBox;

  static Future<void> saveAttendanceRecord(AttendanceRecord record) async {
    record.updateTimestamp();
    await _attendanceBox.put(record.id, record);
  }

  static Future<void> saveDailyAttendance(
    DailyAttendance dailyAttendance,
  ) async {
    dailyAttendance.updateTimestamp();
    await _dailyAttendanceBox.put(dailyAttendance.id, dailyAttendance);
  }

  static DailyAttendance? getDailyAttendance(DateTime date) {
    return _dailyAttendanceBox.values.cast<DailyAttendance?>().firstWhere(
      (da) =>
          da != null &&
          da.date.year == date.year &&
          da.date.month == date.month &&
          da.date.day == date.day,
      orElse: () => null,
    );
  }

  static List<AttendanceRecord> getStudentAttendance(String studentId) {
    return _attendanceBox.values
        .where((record) => record.studentId == studentId)
        .toList();
  }

  // Behavior notes operations
  static Box<BehaviorNote> get behaviorNotesBox => _behaviorNotesBox;

  static Future<void> saveBehaviorNote(BehaviorNote note) async {
    note.updateTimestamp();
    await _behaviorNotesBox.put(note.id, note);
  }

  static List<BehaviorNote> getStudentBehaviorNotes(String studentId) {
    return _behaviorNotesBox.values
        .where((note) => note.studentId == studentId)
        .toList();
  }

  static Future<void> deleteBehaviorNote(String noteId) async {
    await _behaviorNotesBox.delete(noteId);
  }

  // Grade operations
  static Box<Grade> get gradesBox => _gradesBox;
  static Box<SubjectAverage> get subjectAveragesBox => _subjectAveragesBox;

  static Future<void> saveGrade(Grade grade) async {
    grade.updateTimestamp();
    await _gradesBox.put(grade.id, grade);
  }

  static List<Grade> getStudentGrades(String studentId) {
    return _gradesBox.values
        .where((grade) => grade.studentId == studentId)
        .toList();
  }

  static List<Grade> getStudentGradesBySubject(
    String studentId,
    String subject,
  ) {
    return _gradesBox.values
        .where(
          (grade) => grade.studentId == studentId && grade.subject == subject,
        )
        .toList();
  }

  static Future<void> saveSubjectAverage(SubjectAverage average) async {
    await _subjectAveragesBox.put(average.id, average);
  }

  static Future<void> deleteGrade(String gradeId) async {
    await _gradesBox.delete(gradeId);
  }

  // Rubric operations
  static Box<Rubric> get rubricsBox => _rubricsBox;
  static Box<RubricAssessment> get rubricAssessmentsBox =>
      _rubricAssessmentsBox;

  static Future<void> saveRubric(Rubric rubric) async {
    rubric.updateTimestamp();
    await _rubricsBox.put(rubric.id, rubric);
  }

  static Future<void> saveRubricAssessment(RubricAssessment assessment) async {
    assessment.updateTimestamp();
    await _rubricAssessmentsBox.put(assessment.id, assessment);
  }

  static List<Rubric> get allRubrics => _rubricsBox.values.toList();
  static List<RubricAssessment> getStudentRubricAssessments(String studentId) {
    return _rubricAssessmentsBox.values
        .where((assessment) => assessment.studentId == studentId)
        .toList();
  }

  // Lesson operations
  static Box<Lesson> get lessonsBox => _lessonsBox;

  static Future<void> saveLesson(Lesson lesson) async {
    lesson.updateTimestamp();
    await _lessonsBox.put(lesson.id, lesson);
  }

  static List<Lesson> get allLessons => _lessonsBox.values.toList();

  static List<Lesson> getLessonsForDate(DateTime date) {
    return _lessonsBox.values
        .where(
          (lesson) =>
              lesson.startTime.year == date.year &&
              lesson.startTime.month == date.month &&
              lesson.startTime.day == date.day,
        )
        .toList();
  }

  static List<Lesson> getLessonsForWeek(DateTime startOfWeek) {
    final endOfWeek = startOfWeek.add(const Duration(days: 7));
    return _lessonsBox.values
        .where(
          (lesson) =>
              lesson.startTime.isAfter(startOfWeek) &&
              lesson.startTime.isBefore(endOfWeek),
        )
        .toList();
  }

  static Future<void> deleteLesson(String lessonId) async {
    await _lessonsBox.delete(lessonId);
  }

  // Feedback template operations
  static Box<FeedbackTemplate> get feedbackTemplatesBox =>
      _feedbackTemplatesBox;

  static Future<void> saveFeedbackTemplate(FeedbackTemplate template) async {
    template.updateTimestamp();
    await _feedbackTemplatesBox.put(template.id, template);
  }

  static List<FeedbackTemplate> get activeFeedbackTemplates =>
      _feedbackTemplatesBox.values
          .where((template) => template.isActive)
          .toList();

  static List<FeedbackTemplate> getFeedbackTemplatesByCategory(
    FeedbackCategory category,
  ) {
    return _feedbackTemplatesBox.values
        .where((template) => template.category == category && template.isActive)
        .toList();
  }

  // Subject operations
  static Box<Subject> get subjectsBox => _subjectsBox;

  static List<Subject> get allSubjects =>
      _subjectsBox.values.where((s) => s.isActive).toList();

  static Future<void> saveSubject(Subject subject) async {
    subject.updatedAt = DateTime.now();
    await _subjectsBox.put(subject.id, subject);
  }

  static Future<void> deleteSubject(String subjectId) async {
    final subject = _subjectsBox.get(subjectId);
    if (subject != null) {
      subject.isActive = false;
      subject.updatedAt = DateTime.now();
      await _subjectsBox.put(subjectId, subject);
    }
  }

  static Subject? getSubject(String subjectId) {
    return _subjectsBox.get(subjectId);
  }

  static List<Subject> getSubjectsByCategory(CourseCategory category) {
    return _subjectsBox.values
        .where((subject) => subject.category == category && subject.isActive)
        .toList();
  }

  // Course operations
  static Box<Course> get coursesBox => _coursesBox;

  static List<Course> get allCourses =>
      _coursesBox.values.where((c) => c.isActive).toList();

  static Future<void> saveCourse(Course course) async {
    course.updatedAt = DateTime.now();
    await _coursesBox.put(course.id, course);
  }

  static Future<void> deleteCourse(String courseId) async {
    final course = _coursesBox.get(courseId);
    if (course != null) {
      course.isActive = false;
      course.updatedAt = DateTime.now();
      await _coursesBox.put(courseId, course);
    }
  }

  static Course? getCourse(String courseId) {
    return _coursesBox.get(courseId);
  }

  static List<Course> getCoursesBySubject(String subjectId) {
    return _coursesBox.values
        .where((course) => course.subjectId == subjectId && course.isActive)
        .toList();
  }

  static List<Course> getCoursesByLevel(EducationLevel level) {
    return _coursesBox.values
        .where((course) => course.level == level && course.isActive)
        .toList();
  }

  static List<Course> getCoursesByAcademicYear(String academicYear) {
    return _coursesBox.values
        .where(
          (course) => course.academicYear == academicYear && course.isActive,
        )
        .toList();
  }

  // Utility methods
  static Future<void> clearAllData() async {
    await _studentsBox.clear();
    await _attendanceBox.clear();
    await _dailyAttendanceBox.clear();
    await _behaviorNotesBox.clear();
    await _gradesBox.clear();
    await _subjectAveragesBox.clear();
    await _rubricsBox.clear();
    await _rubricAssessmentsBox.clear();
    await _lessonsBox.clear();
    await _feedbackTemplatesBox.clear();
    await _subjectsBox.clear();
    await _coursesBox.clear();

    // Reinitialize default data
    await _initializeDefaultFeedbackTemplates();
    await _initializeDefaultSubjects();
  }

  static Future<void> close() async {
    await Hive.close();
  }

  // Backup and restore methods
  static Future<Map<String, dynamic>> exportData() async {
    return {
      'students': _studentsBox.values.map((s) => s.toJson()).toList(),
      'attendance': _attendanceBox.values.map((a) => a.toJson()).toList(),
      'dailyAttendance': _dailyAttendanceBox.values
          .map((da) => da.toJson())
          .toList(),
      'behaviorNotes': _behaviorNotesBox.values
          .map((bn) => bn.toJson())
          .toList(),
      'grades': _gradesBox.values.map((g) => g.toJson()).toList(),
      'subjectAverages': _subjectAveragesBox.values
          .map((sa) => sa.toJson())
          .toList(),
      'rubrics': _rubricsBox.values.map((r) => r.toJson()).toList(),
      'rubricAssessments': _rubricAssessmentsBox.values
          .map((ra) => ra.toJson())
          .toList(),
      'lessons': _lessonsBox.values.map((l) => l.toJson()).toList(),
      'feedbackTemplates': _feedbackTemplatesBox.values
          .map((ft) => ft.toJson())
          .toList(),
      'subjects': _subjectsBox.values.map((s) => s.toJson()).toList(),
      'courses': _coursesBox.values.map((c) => c.toJson()).toList(),
      'exportDate': DateTime.now().toIso8601String(),
    };
  }

  static Future<void> importData(Map<String, dynamic> data) async {
    // Clear existing data
    await clearAllData();

    // Import students
    if (data['students'] != null) {
      for (final studentJson in data['students']) {
        final student = Student.fromJson(studentJson);
        await _studentsBox.put(student.id, student);
      }
    }

    // Import attendance records
    if (data['attendance'] != null) {
      for (final attendanceJson in data['attendance']) {
        final attendance = AttendanceRecord.fromJson(attendanceJson);
        await _attendanceBox.put(attendance.id, attendance);
      }
    }

    // Import daily attendance
    if (data['dailyAttendance'] != null) {
      for (final dailyAttendanceJson in data['dailyAttendance']) {
        final dailyAttendance = DailyAttendance.fromJson(dailyAttendanceJson);
        await _dailyAttendanceBox.put(dailyAttendance.id, dailyAttendance);
      }
    }

    // Import behavior notes
    if (data['behaviorNotes'] != null) {
      for (final behaviorNoteJson in data['behaviorNotes']) {
        final behaviorNote = BehaviorNote.fromJson(behaviorNoteJson);
        await _behaviorNotesBox.put(behaviorNote.id, behaviorNote);
      }
    }

    // Import grades
    if (data['grades'] != null) {
      for (final gradeJson in data['grades']) {
        final grade = Grade.fromJson(gradeJson);
        await _gradesBox.put(grade.id, grade);
      }
    }

    // Import subject averages
    if (data['subjectAverages'] != null) {
      for (final subjectAverageJson in data['subjectAverages']) {
        final subjectAverage = SubjectAverage.fromJson(subjectAverageJson);
        await _subjectAveragesBox.put(subjectAverage.id, subjectAverage);
      }
    }

    // Import rubrics
    if (data['rubrics'] != null) {
      for (final rubricJson in data['rubrics']) {
        final rubric = Rubric.fromJson(rubricJson);
        await _rubricsBox.put(rubric.id, rubric);
      }
    }

    // Import rubric assessments
    if (data['rubricAssessments'] != null) {
      for (final rubricAssessmentJson in data['rubricAssessments']) {
        final rubricAssessment = RubricAssessment.fromJson(
          rubricAssessmentJson,
        );
        await _rubricAssessmentsBox.put(rubricAssessment.id, rubricAssessment);
      }
    }

    // Import lessons
    if (data['lessons'] != null) {
      for (final lessonJson in data['lessons']) {
        final lesson = Lesson.fromJson(lessonJson);
        await _lessonsBox.put(lesson.id, lesson);
      }
    }

    // Import feedback templates (skip defaults if they exist)
    if (data['feedbackTemplates'] != null) {
      for (final feedbackTemplateJson in data['feedbackTemplates']) {
        final feedbackTemplate = FeedbackTemplate.fromJson(
          feedbackTemplateJson,
        );
        if (!feedbackTemplate.isDefault) {
          await _feedbackTemplatesBox.put(
            feedbackTemplate.id,
            feedbackTemplate,
          );
        }
      }
    }

    // Import subjects (skip defaults if they exist)
    if (data['subjects'] != null) {
      for (final subjectJson in data['subjects']) {
        final subject = Subject.fromJson(subjectJson);
        await _subjectsBox.put(subject.id, subject);
      }
    }

    // Import courses
    if (data['courses'] != null) {
      for (final courseJson in data['courses']) {
        final course = Course.fromJson(courseJson);
        await _coursesBox.put(course.id, course);
      }
    }
  }
}
